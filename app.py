from flask import Flask, render_template, request, redirect, url_for, jsonify, current_app, session, flash
import os
import json
import datetime
import uuid
import pymysql
from werkzeug.security import generate_password_hash, check_password_hash
from functools import wraps
from models import db, Customer, Order, Clothing, ClothingPhoto, RechargeRecord, Staff, init_db
from models import MallCustomer, MallProductDiscount, MallMonthlyBill, MallDiscountHistory, Product, OrderStatusLog
from models import RechargeGiftRule, MemberServiceDiscount
from config import config
from utils import save_base64_image, generate_order_number, update_customer_balance, generate_barcode_base64, calculate_gift_amount
from io import BytesIO
import base64
from PIL import Image, ImageDraw, ImageFont
import re

# 将PyMySQL注册为MySQLdb
pymysql.install_as_MySQLdb()

def create_app(config_name='default'):
    """应用工厂函数 - 创建并配置Flask应用实例

    参数:
        config_name (str): 配置名称，默认为'default'

    返回:
        Flask: 配置好的Flask应用实例
    """
    app = Flask(__name__)

    # =====================================================================
    # 应用配置
    # =====================================================================
    # 加载配置
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    # 确保有密钥用于会话
    if not app.config.get('SECRET_KEY'):
        app.config['SECRET_KEY'] = 'laundry_system_secret_key_for_session'

    # 设置会话过期时间为12小时
    app.config['PERMANENT_SESSION_LIFETIME'] = datetime.timedelta(hours=12)

    # 更新数据库配置为在线数据库
    app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://ytgf:ZEzaJikm2kNakFbk@49.232.0.106:3306/ytgf'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    # 初始化数据库
    init_db(app)

    # =====================================================================
    # 辅助函数和装饰器
    # =====================================================================
    # 登录验证装饰器
    def login_required(f):
        """验证用户是否已登录的装饰器

        如果用户未登录，API请求将返回401错误，
        普通请求将重定向到登录页面

        参数:
            f (function): 被装饰的函数

        返回:
            function: 装饰后的函数
        """
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'staff_id' not in session:
                # 检查是否是API请求
                if request.path.startswith('/api/') or request.headers.get('Accept') == 'application/json' or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'error': '未授权访问', 'redirect': '/login'}), 401

                flash('请先登录', 'error')
                return redirect(url_for('login'))
            return f(*args, **kwargs)
        return decorated_function

    # =====================================================================
    # 认证相关路由
    # =====================================================================
    @app.route('/login', methods=['GET', 'POST'])
    def login():
        """营业员登录

        GET: 返回登录页面
        POST: 处理登录请求，验证用户名和密码

        返回:
            GET: 登录页面
            POST成功: JSON响应，包含成功标志和重定向URL
            POST失败: JSON响应，包含错误信息
        """
        if request.method == 'GET':
            return render_template('login.html')

        if request.method == 'POST':
            try:
                data = request.json
                username = data.get('username')
                password = data.get('password')

                # 验证用户名和密码
                staff = Staff.query.filter_by(username=username).first()

                if not staff:
                    return jsonify({'error': '用户名或密码错误'}), 401

                # 检查密码是否正确
                password_correct = False

                # 尝试使用check_password_hash验证
                try:
                    password_correct = check_password_hash(staff.password, password)
                except Exception as e:
                    pass

                # 如果密码哈希验证失败，尝试直接比较（仅用于调试）
                if not password_correct and password == "admin123" and username == "admin":
                    password_correct = True

                    # 更新密码哈希
                    staff.password = generate_password_hash("admin123")
                    db.session.commit()

                if not password_correct:
                    return jsonify({'error': '用户名或密码错误'}), 401

                if not staff.is_active:
                    return jsonify({'error': '此账号已被禁用，请联系管理员'}), 403

                # 更新最后登录时间
                staff.last_login = datetime.datetime.now()
                db.session.commit()

                # 保存用户信息到session
                session['staff_id'] = staff.id
                session['staff_name'] = staff.name
                session['staff_role'] = staff.role
                session['staff_area'] = staff.area

                return jsonify({'success': True, 'redirect': '/'})
            except Exception as e:
                return jsonify({'error': str(e)}), 500

    @app.route('/logout')
    def logout():
        """登出

        清除用户会话并重定向到登录页面

        返回:
            重定向到登录页面
        """
        session.clear()
        return redirect(url_for('login'))

    @app.route('/')
    @login_required
    def index():
        """主页

        返回应用的主页面

        返回:
            渲染后的主页模板
        """
        staff_name = session.get('staff_name', '未登录')

        # 检测是否是移动设备
        user_agent = request.headers.get('User-Agent', '').lower()
        is_mobile = any(device in user_agent for device in ['android', 'iphone', 'ipad', 'mobile'])

        # 如果是移动设备，重定向到移动版页面
        if is_mobile and not request.args.get('desktop'):
            return redirect(url_for('mobile_index'))

        return render_template('index.html', staff_name=staff_name)

    @app.route('/mobile')
    @login_required
    def mobile_index():
        """移动版主页

        返回移动版的主页面

        返回:
            渲染后的移动版主页模板
        """
        staff_name = session.get('staff_name', '未登录')
        return render_template('mobile_index.html', staff_name=staff_name)

    @app.route('/receipt/<order_id>')
    @login_required
    def receipt(order_id):
        """小票打印预览

        根据订单ID生成小票打印预览

        参数:
            order_id (str): 订单ID

        返回:
            渲染后的小票模板
        """
        try:
            # 获取订单信息
            order = Order.query.get(order_id)
            if not order:
                return "订单不存在", 404

            # 获取客户信息（确保获取最新的余额信息）
            customer = Customer.query.get(order.customer_id)
            if not customer:
                return "客户信息不存在", 404

            # 获取衣物信息
            clothes = Clothing.query.filter_by(order_id=order.id).all()

            # 计算客户余额信息（用于小票显示）
            customer_balance_info = None
            if customer:
                # 计算总余额
                total_balance = (customer.balance or 0) + (customer.gift_balance or 0)

                # 计算订单前后余额
                if order.payment_method == '余额' and order.payment_status == '已付款':
                    # 如果是余额支付，订单前余额 = 当前余额 + 订单金额
                    balance_before_order = total_balance + order.total_amount
                    balance_after_order = total_balance
                    balance_used = order.total_amount
                else:
                    # 如果不是余额支付，余额没有变化
                    balance_before_order = total_balance
                    balance_after_order = total_balance
                    balance_used = 0

                customer_balance_info = {
                    'total_balance': total_balance,
                    'balance': customer.balance or 0,
                    'gift_balance': customer.gift_balance or 0,
                    'balance_before_order': balance_before_order,
                    'balance_after_order': balance_after_order,
                    'balance_used': balance_used,
                    'is_balance_payment': order.payment_method == '余额'
                }

            # 渲染小票模板
            return render_template('receipt.html',
                                  order=order,
                                  customer=customer,
                                  customer_balance_info=customer_balance_info,
                                  clothes=clothes,
                                  is_mobile=True)
        except Exception as e:
            print(f"生成小票出错: {str(e)}")
            return f"生成小票出错: {str(e)}", 500

    @app.route('/labels/<order_id>')
    @login_required
    def labels(order_id):
        """水洗唛打印预览

        根据订单ID生成水洗唛打印预览

        参数:
            order_id (str): 订单ID

        返回:
            JSON: 包含水洗唛HTML和标签数据的JSON对象
        """
        try:
            # 获取订单信息
            order = Order.query.get(order_id)
            if not order:
                return jsonify({"error": "订单不存在"}), 404

            # 获取客户信息
            customer = Customer.query.get(order.customer_id)
            if not customer:
                return jsonify({"error": "客户信息不存在"}), 404

            # 获取衣物信息
            clothes = Clothing.query.filter_by(order_id=order.id).all()

            # 准备标签数据
            labels_data = []
            for i, clothing in enumerate(clothes):
                labels_data.append({
                    "index": i,
                    "clothing_name": clothing.name,
                    "clothing_color": clothing.color,
                    "order_number": order.order_number
                })

            # 渲染水洗唛模板
            html = render_template('labels.html',
                                  order=order,
                                  customer=customer,
                                  clothes=clothes,
                                  is_mobile=True)

            return jsonify({
                "html": html,
                "labels": labels_data
            })
        except Exception as e:
            print(f"生成水洗唛出错: {str(e)}")
            return jsonify({"error": f"生成水洗唛出错: {str(e)}"}), 500

    # =====================================================================
    # 客户管理相关路由
    # =====================================================================
    @app.route('/search_customer')
    @login_required
    def search_customer():
        """搜索客户信息

        根据电话号码查询客户信息

        参数:
            phone (str): 客户电话号码

        返回:
            JSON: 包含客户信息或未找到标志
        """
        phone = request.args.get('phone', '')
        customer = Customer.query.filter_by(phone=phone).first()

        if customer:
            return jsonify({
                'found': True,
                'name': customer.name,
                'id': customer.id,
                'balance': customer.balance,
                'gift_balance': customer.gift_balance or 0.0,
                'total_balance': customer.total_balance
            })
        else:
            return jsonify({'found': False})

    @app.route('/recharge_account', methods=['POST'])
    def recharge_account():
        """客户账户充值

        处理客户账户充值请求，如果客户不存在则创建新客户

        参数:
            phone (str): 客户电话号码
            amount (float): 充值金额
            paymentMethod (str): 支付方式
            customer_name (str, 可选): 客户姓名，用于创建新客户

        返回:
            JSON: 包含充值结果信息
        """
        try:
            data = request.json
            phone = data.get('phone')
            amount = float(data.get('amount', 0))
            payment_method = data.get('paymentMethod')
            customer_name = data.get('customer_name', '')  # 可选参数，用于新客户

            if not phone or amount <= 0 or not payment_method:
                return jsonify({'error': '参数错误'}), 400

            # 查找客户
            customer = Customer.query.filter_by(phone=phone).first()

            # 如果客户不存在且有提供姓名，则创建新客户
            if not customer:
                if not customer_name:
                    customer_name = phone  # 如果没有提供姓名，默认使用电话号码

                print(f"创建新客户: {customer_name}, 电话: {phone}")
                customer = Customer(name=customer_name, phone=phone, balance=0.0)
                db.session.add(customer)
                db.session.flush()  # 获取新客户ID

                is_new_customer = True
            else:
                print(f"找到现有客户: {customer.name}, ID: {customer.id}")
                # 更新mall_customer_id和is_mall_customer字段
                customer.is_mall_customer = True
                customer.mall_customer_id = None
                is_new_customer = False

            # 计算赠送金额（支持用户选择的规则）
            selected_rule_id = data.get('selected_rule_id')
            if selected_rule_id:
                # 用户选择了特定规则
                rule = RechargeGiftRule.query.filter_by(id=selected_rule_id, is_active=True).first()
                if rule and rule.min_amount <= amount:
                    if rule.gift_type == 'percentage':
                        gift_amount = amount * (rule.gift_value / 100.0)
                    else:  # fixed
                        gift_amount = rule.gift_value
                else:
                    gift_amount = 0.0
            else:
                # 使用默认最优规则
                gift_amount = calculate_gift_amount(amount)

            # 更新余额（包含赠送金额）
            new_balance = update_customer_balance(customer, amount, gift_amount=gift_amount)

            # 创建充值记录
            recharge = RechargeRecord(
                customer_id=customer.id,
                amount=amount,
                gift_amount=gift_amount,
                payment_method=payment_method,
                operator=session.get('staff_name', '未知')  # 添加操作员信息
            )

            db.session.add(recharge)
            db.session.commit()

            return jsonify({
                'success': True,
                'newBalance': new_balance,
                'balance': customer.balance,
                'gift_balance': customer.gift_balance,
                'giftAmount': gift_amount,
                'isNewCustomer': is_new_customer,
                'customer_name': customer.name,
                'customer_id': customer.id
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/recharge', methods=['POST'])
    def recharge():
        """移动端客户账户充值

        处理移动端客户账户充值请求，如果客户不存在则创建新客户

        参数:
            phone (str): 客户电话号码
            amount (float): 充值金额
            payment_method (str): 支付方式
            name (str, 可选): 客户姓名，用于创建新客户
            selected_rule_id (int, 可选): 用户选择的赠送规则ID

        返回:
            JSON: 包含充值结果信息
        """
        try:
            data = request.json
            phone = data.get('phone')
            amount = float(data.get('amount', 0))
            payment_method = data.get('payment_method')
            customer_name = data.get('name', '')  # 可选参数，用于新客户

            if not phone or amount <= 0 or not payment_method:
                return jsonify({'error': '参数错误'}), 400

            # 查找或创建客户
            customer = Customer.query.filter_by(phone=phone).first()
            is_new_customer = False

            if not customer:
                # 创建新客户
                customer = Customer(
                    name=customer_name or phone,  # 如果没有提供姓名，使用电话号码
                    phone=phone,
                    balance=0.0,
                    gift_balance=0.0
                )
                db.session.add(customer)
                db.session.flush()  # 获取customer.id
                is_new_customer = True

            # 计算赠送金额（支持用户选择的规则）
            selected_rule_id = data.get('selected_rule_id')
            if selected_rule_id:
                # 用户选择了特定规则
                rule = RechargeGiftRule.query.filter_by(id=selected_rule_id, is_active=True).first()
                if rule and rule.min_amount <= amount:
                    if rule.gift_type == 'percentage':
                        gift_amount = amount * (rule.gift_value / 100.0)
                    else:  # fixed
                        gift_amount = rule.gift_value
                else:
                    gift_amount = 0.0
            else:
                # 使用默认最优规则
                gift_amount = calculate_gift_amount(amount)

            # 更新余额（包含赠送金额）
            new_balance = update_customer_balance(customer, amount, gift_amount=gift_amount)

            # 创建充值记录
            recharge = RechargeRecord(
                customer_id=customer.id,
                amount=amount,
                gift_amount=gift_amount,
                payment_method=payment_method,
                operator=session.get('staff_name', '未知')  # 添加操作员信息
            )

            db.session.add(recharge)
            db.session.commit()

            return jsonify({
                'success': True,
                'newBalance': new_balance,
                'balance': customer.balance,
                'gift_balance': customer.gift_balance,
                'giftAmount': gift_amount,
                'isNewCustomer': is_new_customer,
                'customer_name': customer.name,
                'customer_id': customer.id
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    # =====================================================================
    # 订单处理相关路由
    # =====================================================================
    @app.route('/submit_order', methods=['POST'])
    def submit_order():
        """提交订单

        处理新订单的创建，包括普通客户订单和商场客户订单

        请求体:
            JSON对象，包含订单信息、客户信息和衣物信息

        返回:
            JSON: 包含订单创建结果
        """
        # 检查用户是否登录，如果未登录，返回JSON格式错误
        if 'staff_id' not in session:
            return jsonify({'error': '未登录或会话已过期', 'redirect': '/login'}), 401

        try:
            print("接收到订单提交请求")
            # 获取请求数据
            if not request.is_json:
                print("请求不是JSON格式")
                return jsonify({'error': '请求必须是JSON格式'}), 400

            data = request.json
            print(f"接收到的数据: {data}")

            # 检查是否是商场客户订单
            is_mall_customer = data.get('is_mall_customer', False)

            # 获取客户信息 - 修改为直接从data中获取，而不是从customer对象中获取
            payment_method = data.get('payment_method')
            clothing_items = data.get('items', [])  # 修正字段名称，前端发送的是items而不是clothing_items

            # 初始化mall_customer_id变量
            mall_customer_id = None

            if is_mall_customer:
                # 商场客户订单处理
                mall_customer_id = data.get('mall_customer_id')
                customer_name = data.get('mall_customer_name') or data.get('customer_name')
                customer_phone = data.get('mall_customer_phone') or data.get('customer_phone', '')
                address = data.get('address', '')  # 商场客户通常不需要地址信息，但仍然获取

                # 修改：完善商场客户信息的验证，确保姓名和电话都有提供
                if not mall_customer_id:
                    print("缺少商场客户ID")
                    return jsonify({'error': '缺少商场客户ID'}), 400

                if not customer_name:
                    print("缺少商场客户姓名")
                    return jsonify({'error': '请提供商场客户姓名'}), 400

                if not customer_phone:
                    print("缺少商场客户电话")
                    return jsonify({'error': '请提供商场客户电话'}), 400

                # 获取商场客户信息
                mall_customer = MallCustomer.query.get(mall_customer_id)
                if not mall_customer:
                    print(f"找不到商场客户: ID {mall_customer_id}")
                    return jsonify({'error': '找不到指定的商场客户'}), 404

                print(f"商场客户订单: {mall_customer.mall_name} (ID: {mall_customer.id})")

                # 为了兼容性，创建或更新普通客户记录
                customer = Customer.query.filter_by(phone=customer_phone).first()
                if not customer:
                    print(f"为商场客户创建关联的普通客户记录: {customer_name}")
                    customer = Customer(
                        name=customer_name,
                        phone=customer_phone,
                        balance=0.0,
                        is_mall_customer=True,
                        mall_customer_id=mall_customer_id
                    )
                    db.session.add(customer)
                    db.session.flush()  # 获取新客户ID

                    is_new_customer = True
                else:
                    print(f"找到商场客户关联的普通客户记录: {customer.name}, ID: {customer.id}")
                    # 更新mall_customer_id和is_mall_customer字段
                    customer.is_mall_customer = True
                    customer.mall_customer_id = mall_customer_id
                    is_new_customer = False

            else:
                # 普通客户订单处理
                customer_name = data.get('customer_name')
                customer_phone = data.get('customer_phone')
                address = data.get('address', '')

                if not customer_name or not customer_phone:
                    print("缺少必要的客户信息")
                    return jsonify({'error': '缺少必要的客户信息'}), 400

                # 检查客户是否已存在
                customer = Customer.query.filter_by(phone=customer_phone).first()
                if not customer:
                    print(f"创建新客户: {customer_name}")
                    customer = Customer(name=customer_name, phone=customer_phone, balance=0.0)
                    db.session.add(customer)
                    db.session.flush()  # 获取新客户ID
                else:
                    print(f"找到现有客户: {customer.name}, ID: {customer.id}")

            # 计算原始订单总金额
            original_total_amount = sum(float(item.get('price', 0)) for item in clothing_items)

            # 计算会员折扣（如果是普通客户且不是商场订单）
            member_discount_info = {'total_discount': 0, 'discounted_items': []}
            if not is_mall_customer and customer:
                member_discount_info = calculate_member_discount(customer.id, clothing_items)
                print(f"会员折扣信息: {member_discount_info}")

            # 应用会员折扣到衣物价格
            for i, item in enumerate(clothing_items):
                if i < len(member_discount_info['discounted_items']):
                    discount_item = member_discount_info['discounted_items'][i]
                    # 保存原始价格
                    item['original_price'] = discount_item['original_price']
                    # 更新为折扣后价格
                    item['price'] = discount_item['discounted_price']
                    item['discount_amount'] = discount_item['discount_amount']
                    item['discount_source'] = discount_item['discount_source']

            # 计算最终订单总金额
            total_amount = float(data.get('total_amount', 0))
            if total_amount <= 0:
                # 使用折扣后的价格计算总金额
                total_amount = sum(float(item.get('price', 0)) for item in clothing_items)

            # 记录折扣金额
            discount_amount = member_discount_info['total_discount'] + data.get('discount_amount', 0)

            print(f"原始总金额: {original_total_amount}, 订单总金额: {total_amount}, 折扣金额: {discount_amount}")

            # 如果是余额支付，检查余额是否足够
            if payment_method == '余额' and not is_mall_customer:  # 商场客户通常不使用余额支付
                total_balance = customer.total_balance
                if total_balance < total_amount:
                    print(f"余额不足: {total_balance} < {total_amount}")
                    return jsonify({'error': '账户余额不足'}), 400

                # 更新客户余额（优先使用赠送余额）
                new_balance = update_customer_balance(customer, total_amount, is_recharge=False)
                print(f"更新后的总余额: {new_balance}")

            # 创建订单
            order_number = generate_order_number()
            print(f"生成订单号: {order_number}")

            # 商场客户订单通常是月结
            payment_status = "已付款"
            if is_mall_customer and payment_method == "月结":
                payment_status = "未付款"  # 月结的商场客户订单初始状态为未付款
            elif payment_method == "未付款":
                payment_status = "未付款"

            # 如果订单状态是已付款，设置支付时间
            payment_time = None
            if payment_status == "已付款":
                payment_time = datetime.datetime.now()

            order = Order(
                order_number=order_number,
                customer_id=customer.id,
                total_amount=total_amount,
                payment_method=payment_method,
                payment_status=payment_status,
                payment_time=payment_time,
                address=address,
                operator=session.get('staff_name', '未知'),  # 添加操作员信息
                is_mall_order=is_mall_customer,
                mall_customer_id=customer.mall_customer_id if is_mall_customer else None,
                discount_amount=discount_amount,
                status='门店已分拣'  # 设置初始状态为"门店已分拣"
            )
            db.session.add(order)
            db.session.flush()  # 获取订单ID
            print(f"创建订单成功, ID: {order.id}")

            # 如果是商场客户订单，更新或创建月度账单
            if is_mall_customer:
                # 获取当前年月
                current_date = datetime.datetime.now()
                bill_year_month = current_date.strftime('%Y-%m')

                # 计算当月的开始和结束日期
                import calendar
                _, last_day = calendar.monthrange(current_date.year, current_date.month)
                bill_start_date = datetime.date(current_date.year, current_date.month, 1)
                bill_end_date = datetime.date(current_date.year, current_date.month, last_day)

                # 查找当月账单
                monthly_bill = MallMonthlyBill.query.filter_by(
                    mall_customer_id=customer.mall_customer_id,
                    bill_year_month=bill_year_month
                ).first()

                # 计算订单的原始金额（折扣前）
                original_total_amount = 0
                for item_data in clothing_items:
                    item_original_price = item_data.get('original_price', item_data.get('price', 0))
                    item_quantity = item_data.get('quantity', 1)
                    original_total_amount += float(item_original_price) * item_quantity

                # 如果没有原始价格信息，使用订单总金额作为原始金额
                if original_total_amount == 0:
                    original_total_amount = total_amount

                if monthly_bill:
                    # 更新现有账单
                    monthly_bill.order_count += 1
                    monthly_bill.original_amount += original_total_amount
                    monthly_bill.total_amount += total_amount
                    monthly_bill.discount_amount += discount_amount
                    monthly_bill.actual_amount = monthly_bill.total_amount
                    print(f"更新商场客户月度账单: ID {monthly_bill.id}, 原始金额: {monthly_bill.original_amount}, 实际金额: {monthly_bill.actual_amount}")
                else:
                    # 创建新账单
                    monthly_bill = MallMonthlyBill(
                        mall_customer_id=customer.mall_customer_id,
                        bill_year_month=bill_year_month,
                        bill_start_date=bill_start_date,
                        bill_end_date=bill_end_date,
                        order_count=1,
                        original_amount=original_total_amount,
                        total_amount=total_amount,
                        discount_amount=discount_amount,
                        actual_amount=total_amount,
                        payment_status="未付款"
                    )
                    db.session.add(monthly_bill)
                    print(f"创建商场客户月度账单: {bill_year_month}, 原始金额: {original_total_amount}, 实际金额: {total_amount}")

            # 处理每件衣物
            for i, item_data in enumerate(clothing_items):
                # 提取衣物折扣信息（如果有）
                original_price = item_data.get('original_price')
                discount_rate = item_data.get('discount_rate')

                # 创建衣物记录
                print(f"处理衣物 {i+1}: {item_data.get('name', '')}")

                # 解析服务和特殊需求
                services = item_data.get('serviceTypes', ['洗衣'])
                special_requirements = item_data.get('specialRequirements', {})

                clothing = Clothing(
                    name=item_data.get('name', ''),
                    color=item_data.get('color', ''),
                    services=json.dumps(services),
                    special_requirements=json.dumps(special_requirements),
                    price=float(item_data.get('price', 0)),
                    quantity=int(item_data.get('quantity', 1)),  # 添加数量字段支持
                    remarks=item_data.get('remarks', ''),
                    customer_id=customer.id,
                    order_id=order.id,
                    original_price=original_price,
                    discount_rate=discount_rate,
                    is_mall_order=is_mall_customer
                )
                db.session.add(clothing)
                db.session.flush()  # 获取衣物ID
                print(f"衣物记录创建成功, ID: {clothing.id}")

                # 处理衣物照片
                photos = item_data.get('photos', [])
                print(f"衣物照片数量: {len(photos)}")
                for j, photo_data in enumerate(photos):
                    # 保存图片
                    image_path = save_base64_image(photo_data, customer_phone, j)
                    if image_path:
                        # 创建照片记录
                        photo = ClothingPhoto(
                            clothing_id=clothing.id,
                            image_path=image_path
                        )
                        db.session.add(photo)
                        print(f"添加照片 {j+1} 成功")

            print("提交数据库事务...")
            db.session.commit()
            print("数据库事务提交成功")

            # 构建响应数据
            response_data = {
                'success': True,
                'order_id': order.id,
                'order_number': order.order_number,
                'total_amount': order.total_amount
            }

            # 如果是余额支付，返回新余额
            if payment_method == '余额' and not is_mall_customer:
                response_data['newBalance'] = new_balance

            print(f"返回响应: {response_data}")
            return jsonify(response_data)
        except Exception as e:
            print(f"订单提交出错: {str(e)}")
            import traceback
            traceback.print_exc()
            db.session.rollback()
            # 确保返回JSON格式的错误信息
            return jsonify({'error': f'订单提交出错: {str(e)}'}), 500

    # =====================================================================
    # 订单查询和历史相关路由
    # =====================================================================
    @app.route('/customer_history')
    @login_required
    def customer_history():
        """查询客户历史订单

        根据各种条件查询客户的历史订单，包括电话、姓名、日期、订单号、状态等

        参数:
            phone (str, 可选): 客户电话号码
            name (str, 可选): 客户姓名
            date (str, 可选): 订单日期，格式为YYYY-MM-DD
            order_number (str, 可选): 订单号
            status (str, 可选): 订单状态
            all (str, 可选): 是否查询所有订单，值为'true'或'false'
            page (int, 可选): 页码，默认为1
            per_page (int, 可选): 每页记录数，默认为5

        返回:
            JSON: 包含查询结果的JSON对象
        """
        try:
            # 记录开始时间
            start_time = datetime.datetime.now()
            print(f"开始查询客户历史订单: {start_time}")

            phone = request.args.get('phone', '')
            name = request.args.get('name', '')
            date = request.args.get('date', '')
            order_number = request.args.get('order_number', '')
            status = request.args.get('status', '')
            all_records = request.args.get('all', 'false') == 'true'
            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('per_page', 5))

            # 构建查询
            orders_query = Order.query

            # 获取当前登录用户的角色和ID
            staff_role = session.get('staff_role', '')
            staff_name = session.get('staff_name', '')
            staff_area = session.get('staff_area', '')

            print(f"用户角色: {staff_role}, 区域: {staff_area}, 筛选条件: 手机={phone}, 姓名={name}, 日期={date}, 订单号={order_number}, 状态={status}")

            # 如果用户不是管理员，只能查看自己操作的订单
            if staff_role != 'manager':
                orders_query = orders_query.filter_by(operator=staff_name)
            # 如果用户是区域管理员，只能查看自己区域的订单
            elif staff_role == 'manager' and staff_area and staff_area != '总部':
                # 查找该区域所有用户
                area_staff = Staff.query.filter_by(area=staff_area).all()
                area_staff_names = [user.name for user in area_staff]
                # 筛选该区域用户创建的订单
                orders_query = orders_query.filter(Order.operator.in_(area_staff_names))

            customer_info = None
            # 应用筛选条件
            if order_number:
                # 根据订单号查询
                orders_query = orders_query.filter_by(order_number=order_number)
                # 使用第一个订单的客户作为显示信息
                first_order = orders_query.first()
                if not first_order:
                    print(f"未找到订单号为 {order_number} 的订单")
                    end_time = datetime.datetime.now()
                    execution_time = (end_time - start_time).total_seconds()
                    print(f"查询完成，耗时: {execution_time}秒，结果: 未找到")
                    return jsonify({'found': False})
                customer_info = Customer.query.get(first_order.customer_id)
            elif status:
                # 根据订单状态查询
                orders_query = orders_query.filter_by(status=status)
                # 使用第一个订单的客户作为显示信息
                first_order = orders_query.first()
                if not first_order:
                    print(f"未找到状态为 {status} 的订单")
                    end_time = datetime.datetime.now()
                    execution_time = (end_time - start_time).total_seconds()
                    print(f"查询完成，耗时: {execution_time}秒，结果: 未找到")
                    return jsonify({'found': False})
                customer_info = Customer.query.get(first_order.customer_id)
            elif phone:
                # 根据手机号查询
                customer = Customer.query.filter_by(phone=phone).first()
                if not customer:
                    print(f"未找到电话号码为 {phone} 的客户")
                    end_time = datetime.datetime.now()
                    execution_time = (end_time - start_time).total_seconds()
                    print(f"查询完成，耗时: {execution_time}秒，结果: 未找到")
                    return jsonify({'found': False})
                orders_query = orders_query.filter_by(customer_id=customer.id)
                customer_info = customer
            elif name:
                # 根据客户名查询
                customer = Customer.query.filter(Customer.name.like(f'%{name}%')).first()
                if not customer:
                    print(f"未找到姓名包含 {name} 的客户")
                    end_time = datetime.datetime.now()
                    execution_time = (end_time - start_time).total_seconds()
                    print(f"查询完成，耗时: {execution_time}秒，结果: 未找到")
                    return jsonify({'found': False})
                orders_query = orders_query.filter_by(customer_id=customer.id)
                customer_info = customer
            elif date:
                # 根据日期查询订单
                try:
                    search_date = datetime.datetime.strptime(date, '%Y-%m-%d')
                    next_day = search_date + datetime.timedelta(days=1)

                    orders_query = orders_query.filter(
                        Order.created_at >= search_date,
                        Order.created_at < next_day
                    )

                    # 对于日期筛选，如果没有指定客户，显示所有匹配的订单
                    # 使用第一个订单的客户作为显示信息
                    first_order = orders_query.first()
                    if not first_order:
                        print(f"未找到日期为 {date} 的订单")
                        end_time = datetime.datetime.now()
                        execution_time = (end_time - start_time).total_seconds()
                        print(f"查询完成，耗时: {execution_time}秒，结果: 未找到")
                        return jsonify({'found': False})

                    customer_info = Customer.query.get(first_order.customer_id)
                except Exception as e:
                    print(f"日期格式错误: {str(e)}")
                    end_time = datetime.datetime.now()
                    execution_time = (end_time - start_time).total_seconds()
                    print(f"查询出错，耗时: {execution_time}秒，错误: {str(e)}")
                    return jsonify({'error': f'日期格式错误: {str(e)}'}), 400
            elif all_records:
                # 获取所有订单，按照时间倒序排列
                # 使用系统中的第一个客户作为显示信息（这里只是为了满足前端显示需求）
                first_order = orders_query.first()
                if not first_order:
                    print("未找到任何订单记录")
                    end_time = datetime.datetime.now()
                    execution_time = (end_time - start_time).total_seconds()
                    print(f"查询完成，耗时: {execution_time}秒，结果: 未找到")
                    return jsonify({'found': False})
                customer_info = Customer.query.get(first_order.customer_id)
            else:
                # 如果没有任何筛选条件，请求也不是获取全部，则返回错误
                print("未提供查询条件")
                end_time = datetime.datetime.now()
                execution_time = (end_time - start_time).total_seconds()
                print(f"查询完成，耗时: {execution_time}秒，结果: 参数错误")
                return jsonify({'error': '请提供查询条件或设置all=true参数'}), 400

            # 按创建时间倒序排序
            orders_query = orders_query.order_by(Order.created_at.desc())

            # 计算总数
            total_orders = orders_query.count()
            print(f"符合条件的订单总数: {total_orders}")

            # 如果没有订单，则提前返回未找到
            if total_orders == 0:
                print("未找到符合条件的订单")
                end_time = datetime.datetime.now()
                execution_time = (end_time - start_time).total_seconds()
                print(f"查询完成，耗时: {execution_time}秒，结果: 未找到")
                return jsonify({'found': False})

            # 应用分页
            orders = orders_query.offset((page - 1) * per_page).limit(per_page).all()

            # 批量获取所有订单ID，用于后续查询
            order_ids = [order.id for order in orders]
            customer_ids = [order.customer_id for order in orders]

            # 批量查询所有客户信息
            customers_dict = {}
            customers = Customer.query.filter(Customer.id.in_(customer_ids)).all()
            for customer in customers:
                customers_dict[customer.id] = customer

            # 批量查询所有订单的衣物信息
            all_clothes = {}
            clothes_items = Clothing.query.filter(Clothing.order_id.in_(order_ids)).all()
            for item in clothes_items:
                if item.order_id not in all_clothes:
                    all_clothes[item.order_id] = []
                all_clothes[item.order_id].append(item)

            # 获取所有衣物ID
            clothing_ids = [item.id for item in clothes_items]

            # 批量查询所有衣物的照片
            all_photos = {}
            if clothing_ids:
                photos = ClothingPhoto.query.filter(ClothingPhoto.clothing_id.in_(clothing_ids)).all()
                for photo in photos:
                    if photo.clothing_id not in all_photos:
                        all_photos[photo.clothing_id] = []
                    all_photos[photo.clothing_id].append(photo)

            # 构建订单列表
            order_list = []
            for order in orders:
                customer = customers_dict.get(order.customer_id)
                clothes_list = []

                # 获取该订单的衣物
                clothes = all_clothes.get(order.id, [])

                for item in clothes:
                    # 获取衣物照片
                    photos = all_photos.get(item.id, [])
                    photo_paths = [f"/static/{photo.image_path}" for photo in photos]

                    # 解析服务特殊要求
                    requirements = {}
                    if item.special_requirements:
                        try:
                            requirements = json.loads(item.special_requirements)
                        except:
                            requirements = {}

                    # 解析服务类型
                    services = []
                    if item.services:
                        try:
                            services = json.loads(item.services)
                        except:
                            services = []

                    clothes_list.append({
                        'id': item.id,
                        'name': item.name,
                        'color': item.color,
                        'quantity': item.quantity or 1,  # 添加数量字段
                        'services': services,
                        'requirements': requirements,
                        'price': item.price,
                        'remarks': item.remarks,
                        'photos': photo_paths,
                        'date': item.created_at.strftime('%Y-%m-%d %H:%M')
                    })

                order_list.append({
                    'id': order.id,
                    'order_number': order.order_number,
                    'date': order.created_at.strftime('%Y-%m-%d %H:%M'),
                    'total_amount': order.total_amount,
                    'discount_amount': order.discount_amount,
                    'actual_amount': order.total_amount - order.discount_amount,
                    'payment_method': order.payment_method,
                    'payment_status': order.payment_status,
                    'payment_time': order.payment_time.strftime('%Y-%m-%d %H:%M') if order.payment_time else '',
                    'address': order.address,
                    'status': order.status,
                    'operator': order.operator or '未知',
                    'clothes': clothes_list,
                    'customer_name': customer.name if customer else '未知',
                    'customer_phone': customer.phone if customer else '未知',
                    # 添加订单修改标记信息
                    'is_modified': order.is_modified or False,
                    'last_modified_at': order.last_modified_at.strftime('%Y-%m-%d %H:%M') if order.last_modified_at else '',
                    'last_modified_by': order.last_modified_by or '',
                    'modification_count': order.modification_count or 0
                })

            # 计算执行时间
            end_time = datetime.datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            print(f"查询完成，耗时: {execution_time}秒，返回 {len(order_list)} 条订单记录")

            response_data = {
                'found': True,
                'customer': {
                    'name': customer_info.name,
                    'phone': customer_info.phone,
                    'balance': customer_info.balance
                },
                'orders': order_list,
                'pagination': {
                    'total': total_orders,
                    'page': page,
                    'per_page': per_page,
                    'pages': (total_orders + per_page - 1) // per_page
                },
                'execution_time': execution_time  # 添加执行时间到响应中
            }

            return jsonify(response_data)
        except Exception as e:
            # 记录异常发生时的时间，计算总执行时间
            end_time = datetime.datetime.now()
            execution_time = (end_time - start_time if 'start_time' in locals() else datetime.datetime.now()).total_seconds()

            print(f"客户历史查询出错: {str(e)}, 耗时: {execution_time}秒")
            import traceback
            traceback.print_exc()
            return jsonify({'error': f'查询失败: {str(e)}'}), 500

    @app.route('/history')
    @login_required
    def history():
        """订单历史页面

        返回订单历史查询页面

        返回:
            渲染后的历史页面模板
        """
        return render_template('history.html')

    @app.route('/data_summary')
    @login_required
    def data_summary():
        """数据汇总页面

        返回数据汇总页面

        返回:
            渲染后的数据汇总页面模板
        """
        return render_template('data_summary.html')

    # =====================================================================
    # 数据汇总相关路由
    # =====================================================================
    @app.route('/api/summary_data')
    @login_required
    def get_summary_data():
        """获取汇总数据API

        根据日期范围查询订单和充值数据，生成汇总统计信息

        参数:
            start_date (str, 可选): 开始日期，格式为YYYY-MM-DD，默认为7天前
            end_date (str, 可选): 结束日期，格式为YYYY-MM-DD，默认为当天

        返回:
            JSON: 包含汇总数据的JSON对象
        """
        try:
            # 获取当前登录用户的角色和信息
            staff_role = session.get('staff_role', '')
            staff_name = session.get('staff_name', '')
            staff_area = session.get('staff_area', '')

            print(f"数据汇总权限检查 - 角色: {staff_role}, 姓名: {staff_name}, 区域: {staff_area}")

            # 获取查询参数
            start_date = request.args.get('start_date')
            end_date = request.args.get('end_date')

            # 默认查询近7天数据
            if not start_date:
                start_date = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
            if not end_date:
                end_date = datetime.datetime.now().strftime('%Y-%m-%d')

            # 转换为datetime对象
            start_datetime = datetime.datetime.strptime(f"{start_date} 00:00:00", '%Y-%m-%d %H:%M:%S')
            end_datetime = datetime.datetime.strptime(f"{end_date} 23:59:59", '%Y-%m-%d %H:%M:%S')

            # 构建订单查询，查询已付款的订单（按支付时间筛选），排除余额付款
            orders_query = Order.query.filter(
                Order.payment_time.between(start_datetime, end_datetime),
                Order.payment_status == '已付款',
                Order.payment_method != '余额'
            )

            # 应用权限过滤
            if staff_role != 'manager':
                # 普通营业员只能查看自己操作的订单
                orders_query = orders_query.filter_by(operator=staff_name)
                print(f"普通营业员权限：只查看操作员为 {staff_name} 的订单")
            elif staff_role == 'manager' and staff_area and staff_area != '总部':
                # 区域管理员只能查看自己区域的订单
                area_staff = Staff.query.filter_by(area=staff_area).all()
                area_staff_names = [user.name for user in area_staff]
                orders_query = orders_query.filter(Order.operator.in_(area_staff_names))
                print(f"区域管理员权限：查看区域 {staff_area} 的订单，包含营业员: {area_staff_names}")
            else:
                # 超级管理员或总部管理员可以查看所有订单
                print(f"超级管理员权限：查看所有订单")

            orders = orders_query.all()

            # 查询充值记录（同样应用权限过滤）
            recharge_query = RechargeRecord.query.filter(
                RechargeRecord.created_at.between(start_datetime, end_datetime)
            )

            # 对充值记录也应用相同的权限过滤
            if staff_role != 'manager':
                # 普通营业员只能查看自己操作的充值记录
                recharge_query = recharge_query.filter_by(operator=staff_name)
            elif staff_role == 'manager' and staff_area and staff_area != '总部':
                # 区域管理员只能查看自己区域的充值记录
                area_staff = Staff.query.filter_by(area=staff_area).all()
                area_staff_names = [user.name for user in area_staff]
                recharge_query = recharge_query.filter(RechargeRecord.operator.in_(area_staff_names))

            recharge_records = recharge_query.all()

            # 汇总数据
            summary = {
                'total_orders': len(orders),
                'total_revenue': sum(order.total_amount for order in orders),
                'payment_methods': {},
                'order_status': {},
                'daily_data': {},
                'service_types': {},
                'total_items': 0,  # 总衣物件数
                'total_recharge': sum(record.amount for record in recharge_records),  # 充值总金额
                'recharge_by_method': {},  # 按支付方式统计充值金额
                'operator_stats': {}  # 按营业员统计数据
            }

            # 统计支付方式
            for order in orders:
                # 支付方式统计
                if order.payment_method in summary['payment_methods']:
                    summary['payment_methods'][order.payment_method] += 1
                else:
                    summary['payment_methods'][order.payment_method] = 1

                # 如果是现金支付，计入现金收入
                if order.payment_method == '现金':
                    summary['cash_revenue'] += order.total_amount

                # 订单状态统计
                if order.status in summary['order_status']:
                    summary['order_status'][order.status] += 1
                else:
                    summary['order_status'][order.status] = 1

                # 营业员统计
                operator = order.operator or '未知'
                if operator not in summary['operator_stats']:
                    summary['operator_stats'][operator] = {
                        'orders': 0,
                        'revenue': 0,
                        'items': 0
                    }
                summary['operator_stats'][operator]['orders'] += 1
                summary['operator_stats'][operator]['revenue'] += order.total_amount

                # 日期统计
                date_str = order.payment_time.strftime('%Y-%m-%d')
                if date_str not in summary['daily_data']:
                    summary['daily_data'][date_str] = {
                        'orders': 0,
                        'revenue': 0,
                        'items': 0,
                        'cash_revenue': 0
                    }
                summary['daily_data'][date_str]['orders'] += 1
                summary['daily_data'][date_str]['revenue'] += order.total_amount

                # 如果是现金收入，添加到每日现金收入
                if order.payment_method == '现金':
                    summary['daily_data'][date_str]['cash_revenue'] += order.total_amount

            # 统计服务类型和衣物类型
            # 从付款订单中查询衣物
            order_ids = [order.id for order in orders]
            clothes = Clothing.query.filter(Clothing.order_id.in_(order_ids)).all()

            # 衣物总件数 - 修复：使用数量总和而不是条目数
            summary['total_items'] = sum(item.quantity or 1 for item in clothes)

            for item in clothes:
                # 获取衣物数量
                item_quantity = item.quantity or 1

                # 添加到每日衣物件数 - 修复：使用实际数量
                order = item.order
                date_str = order.payment_time.strftime('%Y-%m-%d')
                if date_str in summary['daily_data']:
                    summary['daily_data'][date_str]['items'] += item_quantity

                # 获取订单对应的营业员，更新衣物件数 - 修复：使用实际数量
                order = item.order
                operator = order.operator or '未知'
                if operator in summary['operator_stats']:
                    summary['operator_stats'][operator]['items'] += item_quantity

                # 服务类型统计
                services = []
                if item.services:
                    try:
                        services = json.loads(item.services)
                    except:
                        services = []

                for service in services:
                    if service in summary['service_types']:
                        summary['service_types'][service] += item_quantity  # 修复：按数量统计
                    else:
                        summary['service_types'][service] = item_quantity



            # 按支付方式统计充值
            for record in recharge_records:
                method = record.payment_method
                if method in summary['recharge_by_method']:
                    summary['recharge_by_method'][method] += record.amount
                else:
                    summary['recharge_by_method'][method] = record.amount

            # 排序数据
            summary['daily_data'] = dict(sorted(summary['daily_data'].items()))
            summary['payment_methods'] = dict(sorted(summary['payment_methods'].items(), key=lambda x: x[1], reverse=True))
            summary['order_status'] = dict(sorted(summary['order_status'].items()))
            summary['service_types'] = dict(sorted(summary['service_types'].items(), key=lambda x: x[1], reverse=True))
            summary['recharge_by_method'] = dict(sorted(summary['recharge_by_method'].items(), key=lambda x: x[1], reverse=True))

            # 对营业员数据按订单数降序排序
            summary['operator_stats'] = dict(sorted(summary['operator_stats'].items(), key=lambda x: x[1]['orders'], reverse=True))

            return jsonify({
                'success': True,
                'data': summary,
                'query_period': {
                    'start_date': start_date,
                    'end_date': end_date
                }
            })

        except Exception as e:
            print(f"获取汇总数据出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/recharge_details')
    @login_required
    def get_recharge_details():
        """获取充值明细数据API

        根据日期范围和权限查询充值记录详情，支持分页

        参数:
            start_date (str, 可选): 开始日期，格式为YYYY-MM-DD
            end_date (str, 可选): 结束日期，格式为YYYY-MM-DD
            page (int, 可选): 页码，默认为1
            per_page (int, 可选): 每页记录数，默认为20

        返回:
            JSON: 包含充值明细列表和分页信息
        """
        try:
            # 获取当前用户信息
            staff_role = session.get('staff_role')
            staff_name = session.get('staff_name')
            staff_area = session.get('staff_area')

            # 获取查询参数
            start_date = request.args.get('start_date')
            end_date = request.args.get('end_date')
            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('per_page', 20))

            # 设置默认日期范围
            if not start_date:
                start_date = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
            if not end_date:
                end_date = datetime.datetime.now().strftime('%Y-%m-%d')

            # 转换为datetime对象
            start_datetime = datetime.datetime.strptime(start_date, '%Y-%m-%d')
            end_datetime = datetime.datetime.strptime(end_date, '%Y-%m-%d') + datetime.timedelta(days=1)

            # 构建查询
            query = db.session.query(RechargeRecord, Customer).join(
                Customer, RechargeRecord.customer_id == Customer.id
            ).filter(
                RechargeRecord.created_at.between(start_datetime, end_datetime)
            )

            # 应用权限过滤
            if staff_role != 'manager':
                # 普通营业员只能查看自己的充值记录
                query = query.filter(RechargeRecord.operator == staff_name)
            elif staff_role == 'manager' and staff_area and staff_area != '总部':
                # 区域管理员只能查看自己区域的充值记录
                area_staff = Staff.query.filter_by(area=staff_area).all()
                area_staff_names = [user.name for user in area_staff]
                query = query.filter(RechargeRecord.operator.in_(area_staff_names))
            # 超级管理员或总部管理员可以查看所有记录

            # 按创建时间降序排序
            query = query.order_by(RechargeRecord.created_at.desc())

            # 分页查询
            pagination = query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )

            # 构建返回数据
            recharge_details = []
            for recharge, customer in pagination.items:
                recharge_details.append({
                    'id': recharge.id,
                    'operator': recharge.operator,
                    'created_at': recharge.created_at.isoformat(),
                    'customer_name': customer.name,
                    'customer_phone': customer.phone,
                    'amount': recharge.amount,
                    'gift_amount': recharge.gift_amount or 0.0,
                    'payment_method': recharge.payment_method,
                    'remarks': recharge.remarks or ''
                })

            return jsonify({
                'success': True,
                'recharge_details': recharge_details,
                'current_page': pagination.page,
                'total_pages': pagination.pages,
                'total_records': pagination.total,
                'per_page': per_page
            })

        except Exception as e:
            print(f"获取充值明细出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/export_recharge_details')
    @login_required
    def export_recharge_details():
        """导出充值明细到Excel

        根据日期范围和权限导出充值记录详情到Excel文件

        参数:
            start_date (str, 可选): 开始日期，格式为YYYY-MM-DD
            end_date (str, 可选): 结束日期，格式为YYYY-MM-DD

        返回:
            Excel文件下载
        """
        try:
            import openpyxl
            from openpyxl.styles import Font, Alignment, PatternFill
            from flask import make_response
            from io import BytesIO

            # 获取当前用户信息
            staff_role = session.get('staff_role')
            staff_name = session.get('staff_name')
            staff_area = session.get('staff_area')

            # 获取查询参数
            start_date = request.args.get('start_date')
            end_date = request.args.get('end_date')

            # 设置默认日期范围
            if not start_date:
                start_date = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
            if not end_date:
                end_date = datetime.datetime.now().strftime('%Y-%m-%d')

            # 转换为datetime对象
            start_datetime = datetime.datetime.strptime(start_date, '%Y-%m-%d')
            end_datetime = datetime.datetime.strptime(end_date, '%Y-%m-%d') + datetime.timedelta(days=1)

            # 构建查询
            query = db.session.query(RechargeRecord, Customer).join(
                Customer, RechargeRecord.customer_id == Customer.id
            ).filter(
                RechargeRecord.created_at.between(start_datetime, end_datetime)
            )

            # 应用权限过滤
            if staff_role != 'manager':
                # 普通营业员只能导出自己的充值记录
                query = query.filter(RechargeRecord.operator == staff_name)
            elif staff_role == 'manager' and staff_area and staff_area != '总部':
                # 区域管理员只能导出自己区域的充值记录
                area_staff = Staff.query.filter_by(area=staff_area).all()
                area_staff_names = [user.name for user in area_staff]
                query = query.filter(RechargeRecord.operator.in_(area_staff_names))
            # 超级管理员或总部管理员可以导出所有记录

            # 按创建时间降序排序
            query = query.order_by(RechargeRecord.created_at.desc())
            results = query.all()

            # 创建Excel工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "充值明细"

            # 设置标题样式
            title_font = Font(bold=True, size=12)
            header_fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            center_alignment = Alignment(horizontal="center", vertical="center")

            # 设置表头
            headers = ['营业员姓名', '充值时间', '客户姓名', '客户电话', '充值金额(元)', '赠送金额(元)', '支付方式', '备注']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = title_font
                cell.fill = header_fill
                cell.alignment = center_alignment

            # 填充数据
            for row, (recharge, customer) in enumerate(results, 2):
                ws.cell(row=row, column=1, value=recharge.operator or '未知')
                ws.cell(row=row, column=2, value=recharge.created_at.strftime('%Y-%m-%d %H:%M:%S'))
                ws.cell(row=row, column=3, value=customer.name)
                ws.cell(row=row, column=4, value=customer.phone)
                ws.cell(row=row, column=5, value=recharge.amount)
                ws.cell(row=row, column=6, value=recharge.gift_amount or 0.0)
                ws.cell(row=row, column=7, value=recharge.payment_method)
                ws.cell(row=row, column=8, value=recharge.remarks or '')

            # 自动调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            # 保存到内存
            output = BytesIO()
            wb.save(output)
            output.seek(0)

            # 创建响应
            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            response.headers['Content-Disposition'] = f'attachment; filename=充值明细_{start_date}_{end_date}.xlsx'

            return response

        except Exception as e:
            print(f"导出充值明细出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({'error': str(e)}), 500

    # =====================================================================
    # 订单详情和打印相关路由
    # =====================================================================
    @app.route('/order_details')
    @login_required
    def order_details():
        """获取指定订单的详细信息，用于打印功能

        根据订单ID获取订单详细信息，包括客户信息、衣物信息和状态变更日志

        参数:
            id (str): 订单ID

        返回:
            JSON: 包含订单详细信息的JSON对象
        """
        try:
            order_id = request.args.get('id')
            if not order_id:
                return jsonify({'error': '未提供订单ID'}), 400

            # 获取订单信息
            order = Order.query.get(order_id)
            if not order:
                return jsonify({'error': '订单不存在'}), 404

            # 获取客户信息
            customer = Customer.query.get(order.customer_id)
            if not customer:
                return jsonify({'error': '客户信息不存在'}), 404

            # 获取衣物信息
            clothes = Clothing.query.filter_by(order_id=order.id).all()
            clothes_list = []

            for item in clothes:
                # 获取衣物照片
                photos = ClothingPhoto.query.filter_by(clothing_id=item.id).all()
                photo_paths = [f"/static/{photo.image_path}" for photo in photos]

                # 解析服务特殊要求
                requirements = {}
                if item.special_requirements:
                    try:
                        requirements = json.loads(item.special_requirements)
                    except:
                        requirements = {}

                # 解析服务类型
                services = []
                if item.services:
                    try:
                        services = json.loads(item.services)
                    except:
                        services = []

                clothes_list.append({
                    'id': item.id,
                    'name': item.name,
                    'color': item.color,
                    'quantity': item.quantity or 1,  # 添加数量字段
                    'services': services,
                    'requirements': requirements,
                    'price': item.price,
                    'remarks': item.remarks,
                    'photos': photo_paths,
                    'date': item.created_at.strftime('%Y-%m-%d %H:%M')
                })

            # 获取状态变更日志
            status_logs = OrderStatusLog.query.filter_by(order_id=order.id).order_by(OrderStatusLog.created_at.desc()).all()
            status_logs_list = []
            for log in status_logs:
                status_logs_list.append({
                    'id': log.id,
                    'old_status': log.old_status,
                    'new_status': log.new_status,
                    'changed_by': log.changed_by,
                    'remarks': log.remarks,
                    'created_at': log.created_at.strftime('%Y-%m-%d %H:%M')
                })

            # 计算客户余额信息（用于小票打印）
            customer_balance_info = None
            if customer:
                # 计算总余额（充值余额 + 赠送余额）
                total_balance = (customer.balance or 0) + (customer.gift_balance or 0)

                # 计算订单前后余额
                if order.payment_method == '余额' and order.payment_status == '已付款':
                    # 如果是余额支付，订单前余额 = 当前余额 + 订单金额
                    balance_before_order = total_balance + order.total_amount
                    balance_after_order = total_balance
                    balance_used = order.total_amount
                else:
                    # 如果不是余额支付，余额没有变化
                    balance_before_order = total_balance
                    balance_after_order = total_balance
                    balance_used = 0

                customer_balance_info = {
                    'has_balance_account': True,
                    'total_balance': total_balance,
                    'balance': customer.balance or 0,
                    'gift_balance': customer.gift_balance or 0,
                    'balance_before_order': balance_before_order,
                    'balance_after_order': balance_after_order,
                    'balance_used': balance_used,
                    'is_balance_payment': order.payment_method == '余额'
                }

            # 构建响应数据
            order_data = {
                'id': order.id,
                'order_number': order.order_number,
                'date': order.created_at.strftime('%Y-%m-%d %H:%M'),
                'total_amount': order.total_amount,  # 实际应付金额（折扣后）
                'discount_amount': order.discount_amount or 0,  # 折扣金额
                'original_amount': (order.total_amount or 0) + (order.discount_amount or 0),  # 原始金额（折扣前）
                'actual_amount': order.total_amount,  # 实付金额等于total_amount（折扣后的金额）
                'payment_method': order.payment_method,
                'payment_status': order.payment_status,
                'payment_time': order.payment_time.strftime('%Y-%m-%d %H:%M') if order.payment_time else '',
                'address': order.address,
                'status': order.status,
                'operator': order.operator or '未知',
                'customer_name': customer.name,
                'customer_phone': customer.phone,
                'customer_balance_info': customer_balance_info,  # 添加客户余额信息
                'clothes': clothes_list,
                'status_logs': status_logs_list,
                'remarks': '',  # 添加备注字段，初始为空
                # 添加订单修改标记信息
                'is_modified': order.is_modified or False,
                'last_modified_at': order.last_modified_at.strftime('%Y-%m-%d %H:%M') if order.last_modified_at else '',
                'last_modified_by': order.last_modified_by or '',
                'modification_count': order.modification_count or 0
            }

            # 收集衣物备注作为订单备注
            clothing_remarks = []
            for item in clothes_list:
                if item.get('remarks') and item['remarks'].strip():
                    clothing_remarks.append(f"{item['name']}: {item['remarks']}")

            # 如果有衣物备注，则合并为订单备注
            if clothing_remarks:
                order_data['remarks'] = '; '.join(clothing_remarks)

            return jsonify(order_data)
        except Exception as e:
            print(f"获取订单详情出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({'error': f'获取订单详情失败: {str(e)}'}), 500

    @app.route('/barcode/<order_number>/<int:index>')
    def generate_barcode(order_number, index):
        """生成订单条码/二维码

        根据订单号和衣物索引生成条形码图像

        参数:
            order_number (str): 订单号
            index (int): 衣物索引

        返回:
            图像: PNG格式的条形码图像
        """
        try:
            # 使用已有的工具函数生成条形码
            barcode_base64 = generate_barcode_base64(
                order_number=order_number,
                item_index=index,
                # 以下参数可选，可以根据需要传递
                operator_name="",
                phone="",
                remarks="",
                defects=""
            )

            if not barcode_base64:
                current_app.logger.error("生成条形码失败")
                return "条形码生成错误", 500

            # 从base64字符串中提取图像数据
            if barcode_base64.startswith('data:image'):
                # 分离metadata和实际数据
                barcode_base64 = barcode_base64.split(',')[1]

            # 解码base64数据
            img_data = base64.b64decode(barcode_base64)

            # 创建字节流
            img_io = BytesIO(img_data)
            img_io.seek(0)

            # 返回图像
            return current_app.response_class(img_io, mimetype='image/png')
        except Exception as e:
            current_app.logger.error(f"生成条形码错误: {str(e)}")
            return "条形码生成错误", 500

    @app.route('/update_clothing_item', methods=['POST'])
    @login_required
    def update_clothing_item():
        """更新衣物信息

        更新订单中的衣物信息，包括名称、颜色、价格、备注等

        请求体:
            JSON对象，包含衣物ID、订单ID、名称、价格等信息

        返回:
            JSON: 包含更新结果的JSON对象
        """
        try:
            # 获取请求数据
            data = request.json
            if not data:
                return jsonify({'success': False, 'error': '无效的请求数据'}), 400

            # 验证必要字段
            required_fields = ['id', 'order_id', 'name', 'price']
            for field in required_fields:
                if field not in data:
                    return jsonify({'success': False, 'error': f'缺少必要字段: {field}'}), 400

            # 查找衣物记录
            clothing = Clothing.query.get(data['id'])
            if not clothing:
                return jsonify({'success': False, 'error': '未找到衣物记录'}), 404

            # 检查衣物是否属于指定订单
            if clothing.order_id != data['order_id']:
                return jsonify({'success': False, 'error': '衣物不属于指定订单'}), 403

            # 获取订单信息
            order = Order.query.get(clothing.order_id)
            if not order:
                return jsonify({'success': False, 'error': '未找到关联订单'}), 404

            # 计算价格差异以更新订单总金额
            price_difference = float(data['price']) - clothing.price

            # 更新衣物基本信息
            clothing.name = data['name']
            clothing.color = data.get('color', '')
            clothing.price = float(data['price'])
            clothing.remarks = data.get('remarks', '')

            # 更新服务类型
            clothing.services = json.dumps(data.get('services', []))

            # 更新特殊要求
            if 'requirements' in data:
                clothing.special_requirements = json.dumps(data['requirements'])

            # 更新订单总金额
            order.total_amount = float(order.total_amount) + price_difference

            # 更新订单修改标记
            order.is_modified = True
            order.last_modified_at = datetime.datetime.now()
            order.last_modified_by = session.get('staff_name', '未知')
            order.modification_count = (order.modification_count or 0) + 1

            # 保存更改
            db.session.commit()

            return jsonify({
                'success': True,
                'message': '衣物信息更新成功',
                'new_price': clothing.price,
                'new_total': order.total_amount
            })

        except Exception as e:
            db.session.rollback()
            print(f"更新衣物信息错误: {e}")
            return jsonify({'success': False, 'error': f'更新衣物信息失败: {str(e)}'}), 500

    @app.route('/reset_admin', methods=['GET'])
    def reset_admin():
        """重置管理员账号（仅开发环境使用）"""
        if app.config['ENV'] != 'development':
            return jsonify({'error': '此功能仅在开发环境可用'}), 403

        try:
            # 尝试查找管理员账号
            admin = Staff.query.filter_by(username="admin").first()

            if admin:
                # 更新现有账号
                admin.password = generate_password_hash("admin123")
                admin.name = "系统管理员"
                admin.role = "manager"
                admin.is_active = True
                db.session.commit()
                return jsonify({
                    'success': True,
                    'message': '管理员账号已重置',
                    'username': 'admin',
                    'password': 'admin123'
                })
            else:
                # 创建新账号
                new_admin = Staff(
                    username="admin",
                    password=generate_password_hash("admin123"),
                    name="系统管理员",
                    role="manager",
                    area="总部",
                    is_active=True
                )
                db.session.add(new_admin)
                db.session.commit()
                return jsonify({
                    'success': True,
                    'message': '管理员账号已创建',
                    'username': 'admin',
                    'password': 'admin123'
                })
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': f'重置管理员账号失败: {str(e)}'}), 500

    # =====================================================================
    # 用户管理相关路由
    # =====================================================================
    @app.route('/user_management')
    @login_required
    def user_management():
        """用户管理页面

        返回用户管理界面，仅管理员可访问

        返回:
            渲染后的用户管理页面模板或重定向到首页
        """
        # 检查是否为管理员
        if session.get('staff_role') != 'manager':
            flash('您没有权限访问此页面', 'error')
            return redirect(url_for('index'))

        staff_name = session.get('staff_name', '未登录')
        return render_template('user_management.html', staff_name=staff_name)

    @app.route('/api/users')
    @login_required
    def get_users():
        """获取用户列表API

        获取系统中的用户列表，支持分页、关键字搜索和区域筛选

        参数:
            page (int, 可选): 页码，默认为1
            per_page (int, 可选): 每页记录数，默认为10
            keyword (str, 可选): 搜索关键字，用于搜索用户名、姓名或电话
            area (str, 可选): 区域筛选

        返回:
            JSON: 包含用户列表和分页信息的JSON对象
        """
        try:
            # 记录开始时间
            start_time = datetime.datetime.now()
            print(f"开始获取用户列表: {start_time}")

            # 获取当前用户信息
            current_user_role = session.get('staff_role')
            current_user_area = session.get('staff_area')
            current_user_id = session.get('staff_id')

            # 检查是否为管理员
            if current_user_role != 'manager':
                return jsonify({'error': '权限不足'}), 403

            # 获取查询参数
            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('per_page', 10))
            keyword = request.args.get('keyword', '')
            area = request.args.get('area', '')

            # 构建查询
            query = Staff.query

            # 区域管理员只能查看自己区域的用户
            if current_user_role == 'manager' and current_user_area and current_user_area != '总部':
                query = query.filter(Staff.area == current_user_area)

            # 如果有区域筛选参数，添加过滤条件
            if area:
                query = query.filter(Staff.area == area)

            # 如果有关键字，添加过滤条件
            if keyword:
                query = query.filter(
                    (Staff.username.like(f'%{keyword}%')) |
                    (Staff.name.like(f'%{keyword}%')) |
                    (Staff.phone.like(f'%{keyword}%'))
                )

            # 获取总数
            total = query.count()

            # 如果没有记录，直接返回空结果
            if total == 0:
                end_time = datetime.datetime.now()
                execution_time = (end_time - start_time).total_seconds()
                print(f"用户列表查询完成，耗时: {execution_time}秒，无匹配记录")

                return jsonify({
                    'success': True,
                    'users': [],
                    'pagination': {
                        'total': 0,
                        'page': page,
                        'per_page': per_page,
                        'pages': 0
                    },
                    'execution_time': execution_time
                })

            # 获取分页数据 - 一次性查询所有需要的数据
            users = query.order_by(Staff.id).offset((page - 1) * per_page).limit(per_page).all()

            # 构建用户列表 - 直接从查询结果构建，避免循环中再次查询数据库
            user_list = []
            for user in users:
                user_list.append({
                    'id': user.id,
                    'username': user.username,
                    'name': user.name,
                    'phone': user.phone,
                    'role': user.role,
                    'area': user.area,
                    'is_active': user.is_active,
                    'created_at': user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else None,
                    'last_login': user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else None
                })

            # 计算执行时间
            end_time = datetime.datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            print(f"用户列表查询完成，耗时: {execution_time}秒，返回 {len(user_list)} 条记录")

            return jsonify({
                'success': True,
                'users': user_list,
                'pagination': {
                    'total': total,
                    'page': page,
                    'per_page': per_page,
                    'pages': (total + per_page - 1) // per_page
                },
                'execution_time': execution_time  # 添加执行时间到响应中，方便前端调试
            })
        except Exception as e:
            import traceback
            print(f"获取用户列表出错: {str(e)}")
            print(traceback.format_exc())
            return jsonify({'error': str(e)}), 500

    @app.route('/api/users/<int:user_id>', methods=['GET'])
    @login_required
    def get_user(user_id):
        """获取单个用户信息"""
        try:
            # 获取当前用户信息
            current_user_role = session.get('staff_role')
            current_user_area = session.get('staff_area')
            current_user_id = session.get('staff_id')

            # 检查是否为管理员
            if current_user_role != 'manager':
                return jsonify({'error': '权限不足'}), 403

            # 获取目标用户信息
            user = Staff.query.get(user_id)
            if not user:
                return jsonify({'error': '用户不存在'}), 404

            # 区域管理员只能查看自己区域的用户
            if current_user_role == 'manager' and current_user_area and current_user_area != '总部' and user.area != current_user_area:
                return jsonify({'error': '权限不足，您只能查看本区域的用户'}), 403

            user_data = {
                'id': user.id,
                'username': user.username,
                'name': user.name,
                'phone': user.phone,
                'role': user.role,
                'area': user.area,
                'is_active': user.is_active,
                'created_at': user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else None,
                'last_login': user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else None
            }

            return jsonify({
                'success': True,
                'user': user_data
            })
        except Exception as e:
            print(f"获取用户信息出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/users', methods=['POST'])
    @login_required
    def create_user():
        """创建新用户"""
        try:
            # 获取当前用户信息
            current_user_role = session.get('staff_role')
            current_user_area = session.get('staff_area')

            # 检查是否为管理员
            if current_user_role != 'manager':
                return jsonify({'error': '权限不足'}), 403

            # 获取请求数据
            data = request.json
            if not data:
                return jsonify({'error': '无效的请求数据'}), 400

            # 验证必要字段
            required_fields = ['username', 'name', 'password', 'role']
            for field in required_fields:
                if field not in data:
                    return jsonify({'error': f'缺少必要字段: {field}'}), 400

            # 区域管理员只能创建自己区域的用户
            target_area = data.get('area', '')
            if current_user_role == 'manager' and current_user_area and current_user_area != '总部':
                # 如果区域管理员尝试创建其他区域的用户，强制设置为自己的区域
                if target_area != current_user_area:
                    data['area'] = current_user_area

            # 检查用户名是否已存在
            existing_user = Staff.query.filter_by(username=data['username']).first()
            if existing_user:
                return jsonify({'error': '用户名已存在'}), 400

            # 创建新用户
            new_user = Staff(
                username=data['username'],
                name=data['name'],
                password=generate_password_hash(data['password']),
                phone=data.get('phone', ''),
                role=data['role'],
                area=data.get('area', ''),
                is_active=data.get('is_active', True)
            )

            db.session.add(new_user)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': '用户创建成功',
                'user_id': new_user.id
            })
        except Exception as e:
            db.session.rollback()
            print(f"创建用户出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/users/<int:user_id>', methods=['PUT'])
    @login_required
    def update_user(user_id):
        """更新用户信息"""
        try:
            # 获取当前用户信息
            current_user_role = session.get('staff_role')
            current_user_area = session.get('staff_area')

            # 检查是否为管理员
            if current_user_role != 'manager':
                return jsonify({'error': '权限不足'}), 403

            # 获取请求数据
            data = request.json
            if not data:
                return jsonify({'error': '无效的请求数据'}), 400

            # 查找用户
            user = Staff.query.get(user_id)
            if not user:
                return jsonify({'error': '用户不存在'}), 404

            # 区域管理员只能更新自己区域的用户
            if current_user_role == 'manager' and current_user_area and current_user_area != '总部' and user.area != current_user_area:
                return jsonify({'error': '权限不足，您只能更新本区域的用户'}), 403

            # 区域管理员不能更改用户的区域
            if current_user_role == 'manager' and current_user_area and current_user_area != '总部':
                if 'area' in data and data['area'] != current_user_area:
                    return jsonify({'error': '您不能将用户分配到其他区域'}), 403

            # 如果更新用户名，检查是否与其他用户冲突
            if 'username' in data and data['username'] != user.username:
                existing_user = Staff.query.filter_by(username=data['username']).first()
                if existing_user and existing_user.id != user_id:
                    return jsonify({'error': '用户名已存在'}), 400

                user.username = data['username']

            # 更新其他字段
            if 'name' in data:
                user.name = data['name']

            if 'phone' in data:
                user.phone = data['phone']

            if 'role' in data:
                user.role = data['role']

            if 'area' in data:
                user.area = data['area']

            if 'is_active' in data:
                user.is_active = data['is_active']

            # 如果提供了密码，更新密码
            if 'password' in data and data['password']:
                user.password = generate_password_hash(data['password'])

            db.session.commit()

            return jsonify({
                'success': True,
                'message': '用户信息更新成功'
            })
        except Exception as e:
            db.session.rollback()
            print(f"更新用户信息出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/users/<int:user_id>', methods=['DELETE'])
    @login_required
    def delete_user(user_id):
        """删除用户"""
        try:
            # 获取当前用户信息
            current_user_role = session.get('staff_role')
            current_user_area = session.get('staff_area')
            current_user_id = session.get('staff_id')

            # 检查是否为管理员
            if current_user_role != 'manager':
                return jsonify({'error': '权限不足'}), 403

            # 查找用户
            user = Staff.query.get(user_id)
            if not user:
                return jsonify({'error': '用户不存在'}), 404

            # 不允许删除自己
            if user_id == current_user_id:
                return jsonify({'error': '不能删除当前登录的用户'}), 400

            # 区域管理员只能删除自己区域的用户
            if current_user_role == 'manager' and current_user_area and current_user_area != '总部' and user.area != current_user_area:
                return jsonify({'error': '权限不足，您只能删除本区域的用户'}), 403

            # 删除用户
            db.session.delete(user)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': '用户删除成功'
            })
        except Exception as e:
            db.session.rollback()
            print(f"删除用户出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({'success': False, 'error': str(e)}), 500

    # 在create_app函数内部合适的位置添加状态流转验证函数
    def validate_status_change(old_status, new_status):
        """验证状态变更是否合法"""
        # 定义状态流转规则
        status_flow = {
            '门店已分拣': ['送至工厂中', '已取消'],
            '送至工厂中': ['入厂', '已取消'],
            '入厂': ['工厂分拣', '已取消'],
            '工厂分拣': ['出厂', '已取消'],
            '出厂': ['送至分店中', '已取消'],
            '送至分店中': ['已送至分店', '已取消'],
            '已送至分店': ['已上架', '已取消'],
            '已上架': ['配送中', '已自取', '已取消'],
            '配送中': ['已配送', '已取消', '已退赔'],
            '已取消': ['已退赔'],
            '已退赔': [],  # 终态
            '已自取': [],  # 终态
            '已配送': []   # 终态
        }

        # 如果是管理员或相同状态，不受限制
        if session.get('staff_role') == 'manager' or old_status == new_status:
            return True

        # 检查新状态是否在允许的流转列表中
        return new_status in status_flow.get(old_status, [])

    @app.route('/update_order_status', methods=['POST'])
    @login_required
    def update_order_status():
        """更新订单状态和支付状态"""
        try:
            # 获取请求数据
            data = request.get_json()
            order_id = data.get('order_id')
            status = data.get('status')
            payment_status = data.get('payment_status')
            remarks = data.get('remarks', '')

            if not order_id:
                return jsonify({'error': '未提供订单ID'}), 400

            # 查询订单
            order = Order.query.get(order_id)
            if not order:
                return jsonify({'error': '订单不存在'}), 404

            # 记录旧状态，用于日志
            old_status = order.status
            old_payment_status = order.payment_status
            status_updated = False

            # 更新订单状态(如果提供)
            if status and status != old_status:
                # 记录状态变更日志
                status_log = OrderStatusLog(
                    order_id=order.id,
                    old_status=old_status,
                    new_status=status,
                    changed_by=session.get('staff_name', '未知'),
                    remarks=remarks
                )
                db.session.add(status_log)

                # 更新订单状态
                order.status = status
                status_updated = True

            # 更新支付状态(如果提供)
            if payment_status:
                # 如果支付状态从"未付款"变更为"已付款"，记录支付时间
                if payment_status == '已付款' and order.payment_status != '已付款':
                    order.payment_time = datetime.datetime.now()
                order.payment_status = payment_status

            # 记录操作员信息
            order.updated_at = datetime.datetime.now()

            db.session.commit()

            response_message = '订单已更新'
            if status_updated:
                response_message = f'订单状态已从"{old_status}"更新为"{status}"'
            elif payment_status:
                response_message = f'订单支付状态已更新为"{payment_status}"'

            return jsonify({
                'success': True,
                'message': response_message,
                'order_id': order.id,
                'status': order.status,
                'payment_status': order.payment_status
            })

        except Exception as e:
            db.session.rollback()
            print(f"更新订单状态出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({'error': str(e)}), 500

    @app.route('/batch_update_order_status', methods=['POST'])
    @login_required
    def batch_update_order_status():
        """批量更新订单状态"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'error': '无效的请求数据'}), 400

            order_ids = data.get('order_ids', [])
            status = data.get('status')
            payment_status = data.get('payment_status')
            remarks = data.get('remarks', '批量更新')

            if not order_ids:
                return jsonify({'error': '未提供订单ID'}), 400

            if not status and not payment_status:
                return jsonify({'error': '未提供要更新的状态'}), 400

            # 获取当前登录用户角色和姓名
            staff_role = session.get('staff_role', '')
            staff_name = session.get('staff_name', '')
            staff_area = session.get('staff_area', '')

            # 查找该区域所有用户（如果是区域管理员）
            area_staff_names = []
            if staff_role == 'manager' and staff_area and staff_area != '总部':
                area_staff = Staff.query.filter_by(area=staff_area).all()
                area_staff_names = [user.name for user in area_staff]

            updated_count = 0
            invalid_count = 0
            unauthorized_count = 0

            for order_id in order_ids:
                order = Order.query.get(order_id)
                if order:
                    # 检查当前用户是否有权限更新该订单
                    has_permission = False

                    # 如果是普通员工，只能更新自己操作的订单
                    if staff_role == 'staff' and order.operator == staff_name:
                        has_permission = True
                    # 如果是总部管理员，可以更新所有订单
                    elif staff_role == 'manager' and (not staff_area or staff_area == '总部'):
                        has_permission = True
                    # 如果是区域管理员，可以更新该区域的所有订单
                    elif staff_role == 'manager' and staff_area and order.operator in area_staff_names:
                        has_permission = True

                    if not has_permission:
                        unauthorized_count += 1
                        continue

                    # 只有当提供了状态且状态变更有效时才更新状态
                    if status and status != order.status:
                        if validate_status_change(order.status, status):
                            # 记录旧状态用于日志
                            old_status = order.status
                            # 更新状态
                            order.status = status
                            # 添加状态变更日志
                            status_log = OrderStatusLog(
                                order_id=order.id,
                                old_status=old_status,
                                new_status=status,
                                changed_by=staff_name,
                                remarks=remarks
                            )
                            db.session.add(status_log)
                            updated_count += 1
                        else:
                            invalid_count += 1
                            continue  # 跳过此订单

                    # 更新支付状态
                    if payment_status:
                        # 如果支付状态从"未付款"变更为"已付款"，记录支付时间
                        if payment_status == '已付款' and order.payment_status != '已付款':
                            order.payment_time = datetime.datetime.now()
                        order.payment_status = payment_status
                        if not status or status == order.status:
                            updated_count += 1

            db.session.commit()

            result_message = f'成功更新{updated_count}个订单'
            if invalid_count > 0:
                result_message += f', {invalid_count}个订单因状态流转限制未更新'
            if unauthorized_count > 0:
                result_message += f', {unauthorized_count}个订单因权限不足无法更新'

            return jsonify({
                'success': True,
                'updated_count': updated_count,
                'invalid_count': invalid_count,
                'unauthorized_count': unauthorized_count,
                'message': result_message
            })

        except Exception as e:
            db.session.rollback()
            print(f"批量更新订单状态出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({'error': f'批量更新订单状态失败: {str(e)}'}), 500

    # 商场客户管理相关路由
    @app.route('/mall_customer_management')
    @login_required
    def mall_customer_management():
        """商场客户管理页面"""
        staff_name = session.get('staff_name', '未登录')
        staff_role = session.get('staff_role', '')

        # 限制只有管理员可以访问该页面
        if staff_role != 'manager':
            flash('您没有权限访问该页面', 'error')
            return redirect(url_for('index'))

        return render_template('mall_customer_management.html', staff_name=staff_name)

    @app.route('/api/mall_customers', methods=['GET'])
    @login_required
    def get_mall_customers():
        """获取商场客户列表"""
        try:
            # 获取查询参数
            search_term = request.args.get('search', '')
            status = request.args.get('status', '')
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 10, type=int)

            # 开始时间记录
            start_time = datetime.datetime.now()
            print(f"开始获取商场客户列表: {start_time}")

            # 构建查询 - 使用联表查询一次性获取所需数据
            # 1. 首先获取所有符合条件的商场客户ID
            customer_query = MallCustomer.query

            # 添加调试日志
            print(f"查询参数 - 搜索词: '{search_term}', 状态: '{status}', 页码: {page}, 每页: {per_page}")

            # 添加区域权限控制
            staff_area = session.get('staff_area')
            staff_role = session.get('staff_role')

            # 超级管理员(admin)或总部区域可以查看所有客户，其他角色只能查看自己区域的客户
            if staff_role != 'admin' and staff_area != '总部' and staff_area:
                customer_query = customer_query.filter(MallCustomer.area == staff_area)
                print(f"应用区域过滤: {staff_area}")
            else:
                print(f"管理员权限，查看所有区域客户。角色: {staff_role}, 区域: {staff_area}")

            # 应用搜索过滤器
            if search_term:
                customer_query = customer_query.filter(MallCustomer.mall_name.like(f'%{search_term}%'))

            # 应用状态过滤器
            if status:
                customer_query = customer_query.filter(MallCustomer.status == status)

            # 按名称排序
            customer_query = customer_query.order_by(MallCustomer.mall_name)

            # 分页 - 仅获取当前页面需要的客户IDs
            paginated_customers = customer_query.paginate(page=page, per_page=per_page, error_out=False)
            total_count = paginated_customers.total

            # 获取当前页面的客户ID列表
            customer_ids = [customer.id for customer in paginated_customers.items]
            print(f"分页查询结果 - 总数: {total_count}, 当前页客户IDs: {customer_ids}")

            # 如果没有客户，直接返回空结果
            if not customer_ids:
                return jsonify({
                    'customers': [],
                    'total': 0,
                    'pages': 0,
                    'current_page': page,
                    'per_page': per_page
                })

            # 2. 批量查询这些客户的最新账单 - 使用子查询获取每个客户的最新账单
            latest_bills_subquery = db.session.query(
                MallMonthlyBill.mall_customer_id,
                db.func.max(MallMonthlyBill.bill_year_month).label('latest_bill_month')
            ).filter(
                MallMonthlyBill.mall_customer_id.in_(customer_ids)
            ).group_by(
                MallMonthlyBill.mall_customer_id
            ).subquery()

            # 关联最新账单获取支付状态
            latest_bills = db.session.query(
                MallMonthlyBill.mall_customer_id,
                MallMonthlyBill.payment_status
            ).join(
                latest_bills_subquery,
                db.and_(
                    MallMonthlyBill.mall_customer_id == latest_bills_subquery.c.mall_customer_id,
                    MallMonthlyBill.bill_year_month == latest_bills_subquery.c.latest_bill_month
                )
            ).all()

            # 将账单状态存入字典以便快速查找
            bill_status_dict = {bill[0]: bill[1] for bill in latest_bills}

            # 3. 批量查询这些客户的折扣数量
            discount_counts = db.session.query(
                MallProductDiscount.mall_customer_id,
                db.func.count(MallProductDiscount.id).label('discount_count')
            ).filter(
                MallProductDiscount.mall_customer_id.in_(customer_ids)
            ).group_by(
                MallProductDiscount.mall_customer_id
            ).all()

            # 将折扣数量存入字典以便快速查找
            discount_count_dict = {dc[0]: dc[1] for dc in discount_counts}

            # 重新查询当前页面的完整客户数据，确保按ID排序避免重复
            customers = MallCustomer.query.filter(MallCustomer.id.in_(customer_ids)).order_by(MallCustomer.id).all()

            # 构建结果，使用字典去重确保不会有重复记录
            result = []
            processed_ids = set()
            for customer in customers:
                if customer.id in processed_ids:
                    print(f"警告：发现重复的客户ID {customer.id}，跳过")
                    continue
                processed_ids.add(customer.id)
                # 获取最新账单状态 - 直接从字典中获取
                billing_status = bill_status_dict.get(customer.id, '无账单')

                # 获取折扣数量 - 直接从字典中获取
                discount_count = discount_count_dict.get(customer.id, 0)

                result.append({
                    'id': customer.id,
                    'mall_name': customer.mall_name,
                    'contact_name': customer.contact_name,
                    'phone': customer.phone,
                    'billing_cycle': customer.billing_cycle,
                    'status': customer.status,
                    'contract_end_date': customer.contract_end_date.strftime('%Y-%m-%d') if customer.contract_end_date else '',
                    'billing_status': billing_status,
                    'discount_count': discount_count,
                    'overall_discount_rate': customer.overall_discount_rate,  # 添加整体折扣率
                    'created_at': customer.created_at.strftime('%Y-%m-%d')
                })

            # 计算执行时间
            end_time = datetime.datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            print(f"商场客户列表查询完成，耗时: {execution_time}秒，返回 {len(result)} 条记录")

            return jsonify({
                'customers': result,
                'total': total_count,
                'pages': paginated_customers.pages,
                'current_page': page,
                'per_page': per_page,
                'execution_time': execution_time  # 添加执行时间到响应中，方便前端调试
            })
        except Exception as e:
            import traceback
            print(f"获取商场客户列表出错: {str(e)}")
            print(traceback.format_exc())
            return jsonify({'error': str(e)}), 500

    @app.route('/api/mall_customers', methods=['POST'])
    @login_required
    def create_mall_customer():
        """创建新的商场客户"""
        # 验证权限
        if session.get('staff_role') != 'manager':
            return jsonify({'error': '您没有权限执行此操作'}), 403

        try:
            data = request.json

            # 验证必填字段
            if not data.get('mall_name'):
                return jsonify({'error': '商场品牌名称不能为空'}), 400

            mall_name = data.get('mall_name').strip()
            if not mall_name:
                return jsonify({'error': '商场品牌名称不能为空'}), 400

            # 检查商场名称是否已存在（在同一区域内）
            area = data.get('area') or session.get('staff_area')
            existing_customer = MallCustomer.query.filter_by(
                mall_name=mall_name,
                area=area
            ).first()

            if existing_customer:
                return jsonify({'error': f'商场品牌名称"{mall_name}"在区域"{area}"已存在，请使用不同的名称'}), 400

            # 处理日期字段
            contract_start_date = None
            contract_end_date = None

            if data.get('contract_start_date'):
                contract_start_date = datetime.datetime.strptime(data.get('contract_start_date'), '%Y-%m-%d').date()

            if data.get('contract_end_date'):
                contract_end_date = datetime.datetime.strptime(data.get('contract_end_date'), '%Y-%m-%d').date()

            # 验证区域权限 - 非超级管理员且非总部只能在自己的区域创建客户
            staff_role = session.get('staff_role')
            staff_area = session.get('staff_area')
            if staff_role != 'admin' and staff_area != '总部' and area != staff_area:
                return jsonify({'error': '您只能在自己的区域创建商场客户'}), 403

            # 创建新客户
            new_customer = MallCustomer(
                mall_name=mall_name,  # 使用已验证和清理的商场名称
                address=data.get('address'),
                phone=data.get('phone'),
                contract_start_date=contract_start_date,
                contract_end_date=contract_end_date,
                billing_cycle=data.get('billing_cycle', '月结'),
                contact_name=data.get('contact_name'),
                contact_phone=data.get('contact_phone'),
                contact_position=data.get('contact_position'),
                status=data.get('status', '活跃'),
                remarks=data.get('remarks'),
                area=area
            )

            db.session.add(new_customer)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': '商场客户创建成功',
                'customer_id': new_customer.id
            }), 201
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/mall_customers/<int:customer_id>', methods=['GET'])
    @login_required
    def get_mall_customer(customer_id):
        """获取商场客户详情"""
        try:
            customer = MallCustomer.query.get_or_404(customer_id)

            # 验证区域权限
            staff_area = session.get('staff_area')
            staff_role = session.get('staff_role')
            if staff_role != 'admin' and staff_area != '总部' and staff_area and customer.area != staff_area:
                return jsonify({'error': '您没有权限查看此商场客户'}), 403

            return jsonify({
                'id': customer.id,
                'mall_name': customer.mall_name,
                'address': customer.address,
                'phone': customer.phone,
                'contract_start_date': customer.contract_start_date.strftime('%Y-%m-%d') if customer.contract_start_date else '',
                'contract_end_date': customer.contract_end_date.strftime('%Y-%m-%d') if customer.contract_end_date else '',
                'billing_cycle': customer.billing_cycle,
                'contact_name': customer.contact_name,
                'contact_phone': customer.contact_phone,
                'contact_position': customer.contact_position,
                'status': customer.status,
                'remarks': customer.remarks,
                'overall_discount_rate': customer.overall_discount_rate,
                'area': customer.area,
                'created_at': customer.created_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/mall_customers/<int:customer_id>', methods=['PUT'])
    @login_required
    def update_mall_customer(customer_id):
        """更新商场客户信息"""
        # 验证权限
        if session.get('staff_role') != 'manager':
            return jsonify({'error': '您没有权限执行此操作'}), 403

        try:
            customer = MallCustomer.query.get_or_404(customer_id)

            # 验证区域权限
            staff_area = session.get('staff_area')
            staff_role = session.get('staff_role')
            if staff_role != 'admin' and staff_area != '总部' and staff_area and customer.area != staff_area:
                return jsonify({'error': '您没有权限修改此商场客户'}), 403

            data = request.json

            # 验证区域变更权限
            if 'area' in data and data['area'] != customer.area:
                if staff_role != 'admin' and staff_area != '总部':
                    return jsonify({'error': '您没有权限修改客户所属区域'}), 403

            # 更新基础信息
            if 'mall_name' in data:
                new_mall_name = data['mall_name'].strip()
                if not new_mall_name:
                    return jsonify({'error': '商场品牌名称不能为空'}), 400

                # 检查新名称是否与其他客户重复（排除当前客户）
                if new_mall_name != customer.mall_name:
                    existing_customer = MallCustomer.query.filter(
                        MallCustomer.mall_name == new_mall_name,
                        MallCustomer.area == customer.area,
                        MallCustomer.id != customer.id
                    ).first()

                    if existing_customer:
                        return jsonify({'error': f'商场品牌名称"{new_mall_name}"在区域"{customer.area}"已存在，请使用不同的名称'}), 400

                customer.mall_name = new_mall_name
            if 'address' in data:
                customer.address = data['address']
            if 'phone' in data:
                customer.phone = data['phone']
            if 'contract_start_date' in data and data['contract_start_date']:
                customer.contract_start_date = datetime.datetime.strptime(data['contract_start_date'], '%Y-%m-%d').date()
            if 'contract_end_date' in data and data['contract_end_date']:
                customer.contract_end_date = datetime.datetime.strptime(data['contract_end_date'], '%Y-%m-%d').date()
            if 'billing_cycle' in data:
                customer.billing_cycle = data['billing_cycle']
            if 'contact_name' in data:
                customer.contact_name = data['contact_name']
            if 'contact_phone' in data:
                customer.contact_phone = data['contact_phone']
            if 'contact_position' in data:
                customer.contact_position = data['contact_position']
            if 'status' in data:
                customer.status = data['status']
            if 'remarks' in data:
                customer.remarks = data['remarks']
            if 'overall_discount_rate' in data:
                customer.overall_discount_rate = float(data['overall_discount_rate'])
            if 'area' in data:
                customer.area = data['area']

            db.session.commit()

            return jsonify({
                'success': True,
                'message': '商场客户信息更新成功'
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/mall_customers/<int:customer_id>', methods=['DELETE'])
    @login_required
    def delete_mall_customer(customer_id):
        """删除商场客户"""
        # 验证权限
        if session.get('staff_role') != 'manager':
            return jsonify({'error': '您没有权限执行此操作'}), 403

        try:
            customer = MallCustomer.query.get_or_404(customer_id)

            # 验证区域权限
            staff_area = session.get('staff_area')
            staff_role = session.get('staff_role')
            if staff_role != 'admin' and staff_area != '总部' and staff_area and customer.area != staff_area:
                return jsonify({'error': '您没有权限删除此商场客户'}), 403

            # 检查是否有关联的订单
            related_orders = Order.query.filter_by(mall_customer_id=customer_id).count()
            related_customers = Customer.query.filter_by(mall_customer_id=customer_id).count()

            # 获取强制删除参数
            force_delete = request.args.get('force', 'false').lower() == 'true'

            if (related_orders > 0 or related_customers > 0) and not force_delete:
                error_msg = f'无法删除商场客户"{customer.mall_name}"，存在以下关联数据：\n'
                if related_orders > 0:
                    error_msg += f'• {related_orders} 个关联订单\n'
                if related_customers > 0:
                    error_msg += f'• {related_customers} 个关联的普通客户记录\n'
                error_msg += '\n请确认是否要强制删除（这将清除所有关联数据）？'

                return jsonify({
                    'error': error_msg,
                    'has_relations': True,
                    'related_orders': related_orders,
                    'related_customers': related_customers
                }), 400

            # 如果强制删除，先处理关联数据
            if force_delete:
                # 清除订单中的商场客户关联（将订单转为普通订单）
                if related_orders > 0:
                    Order.query.filter_by(mall_customer_id=customer_id).update({
                        'mall_customer_id': None,
                        'is_mall_order': False
                    })

                # 清除普通客户中的商场客户关联
                if related_customers > 0:
                    Customer.query.filter_by(mall_customer_id=customer_id).update({
                        'mall_customer_id': None,
                        'is_mall_customer': False
                    })

            # 级联删除相关数据
            MallProductDiscount.query.filter_by(mall_customer_id=customer_id).delete()
            MallMonthlyBill.query.filter_by(mall_customer_id=customer_id).delete()
            MallDiscountHistory.query.filter_by(mall_customer_id=customer_id).delete()

            db.session.delete(customer)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': '商场客户已删除'
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    # 产品折扣管理路由
    @app.route('/api/mall_customers/<int:customer_id>/discounts', methods=['GET'])
    @login_required
    def get_customer_discounts(customer_id):
        """获取商场客户的产品折扣列表"""
        try:
            # 验证客户存在和区域权限
            customer = MallCustomer.query.get_or_404(customer_id)
            staff_area = session.get('staff_area')
            staff_role = session.get('staff_role')
            if staff_role != 'admin' and staff_area != '总部' and staff_area and customer.area != staff_area:
                return jsonify({'error': '您没有权限查看此商场客户的折扣信息'}), 403

            discounts = MallProductDiscount.query.filter_by(mall_customer_id=customer_id).all()
            result = []

            for discount in discounts:
                result.append({
                    'id': discount.id,
                    'product_name': discount.product_name,
                    'product_type': discount.product_type,
                    'discount_rate': discount.discount_rate,
                    'effective_date': discount.effective_date.strftime('%Y-%m-%d'),
                    'expiry_date': discount.expiry_date.strftime('%Y-%m-%d') if discount.expiry_date else '长期有效',
                    'created_by': discount.created_by,
                    'created_at': discount.created_at.strftime('%Y-%m-%d'),
                    'change_reason': ''  # 这个字段不在模型中，但前端期望它存在
                })

            return jsonify({
                'discounts': result
            })
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/mall_customers/<int:customer_id>/discounts', methods=['POST'])
    @login_required
    def create_product_discount(customer_id):
        """创建商场客户产品折扣"""
        # 验证权限
        if session.get('staff_role') != 'manager':
            return jsonify({'error': '您没有权限执行此操作'}), 403

        try:
            # 验证客户是否存在和区域权限
            customer = MallCustomer.query.get_or_404(customer_id)
            staff_area = session.get('staff_area')
            staff_role = session.get('staff_role')
            if staff_role != 'admin' and staff_area != '总部' and staff_area and customer.area != staff_area:
                return jsonify({'error': '您没有权限为此商场客户创建折扣'}), 403

            data = request.json

            # 验证必填字段
            required_fields = ['product_name', 'product_type', 'discount_rate', 'effective_date']
            for field in required_fields:
                if field not in data or not data[field]:
                    return jsonify({'error': f'{field} 是必填字段'}), 400

            # 处理日期字段
            effective_date = datetime.datetime.strptime(data['effective_date'], '%Y-%m-%d').date()
            expiry_date = None
            if data.get('expiry_date'):
                expiry_date = datetime.datetime.strptime(data['expiry_date'], '%Y-%m-%d').date()

            # 检查该产品是否已有折扣
            existing_discount = MallProductDiscount.query.filter_by(
                mall_customer_id=customer_id,
                product_name=data['product_name'],
                product_type=data['product_type']
            ).first()

            # 如果存在，记录折扣变更历史
            if existing_discount:
                # 创建折扣变更历史
                discount_history = MallDiscountHistory(
                    mall_customer_id=customer_id,
                    product_name=data['product_name'],
                    product_type=data['product_type'],
                    old_discount_rate=existing_discount.discount_rate,
                    new_discount_rate=float(data['discount_rate']),
                    change_date=datetime.datetime.now().date(),
                    change_reason=data.get('change_reason', '定期调整'),
                    operator=session.get('staff_name', '未知')
                )
                db.session.add(discount_history)

                # 更新折扣
                existing_discount.discount_rate = float(data['discount_rate'])
                existing_discount.effective_date = effective_date
                existing_discount.expiry_date = expiry_date
                existing_discount.updated_at = datetime.datetime.now()

                db.session.commit()

                return jsonify({
                    'success': True,
                    'message': '产品折扣已更新',
                    'discount_id': existing_discount.id
                })

            # 创建新的产品折扣
            new_discount = MallProductDiscount(
                mall_customer_id=customer_id,
                product_name=data['product_name'],
                product_type=data['product_type'],
                discount_rate=float(data['discount_rate']),
                effective_date=effective_date,
                expiry_date=expiry_date,
                created_by=session.get('staff_name', '未知')
            )

            db.session.add(new_discount)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': '产品折扣创建成功',
                'discount_id': new_discount.id
            }), 201
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/discounts/<int:discount_id>', methods=['PUT'])
    @login_required
    def update_product_discount(discount_id):
        """更新产品折扣"""
        # 验证权限
        if session.get('staff_role') != 'manager':
            return jsonify({'error': '您没有权限执行此操作'}), 403

        try:
            discount = MallProductDiscount.query.get_or_404(discount_id)
            data = request.json

            # 如果折扣率有变化，记录历史
            if 'discount_rate' in data and float(data['discount_rate']) != discount.discount_rate:
                # 创建折扣变更历史
                discount_history = MallDiscountHistory(
                    mall_customer_id=discount.mall_customer_id,
                    product_name=discount.product_name,
                    product_type=discount.product_type,
                    old_discount_rate=discount.discount_rate,
                    new_discount_rate=float(data['discount_rate']),
                    change_date=datetime.datetime.now().date(),
                    change_reason=data.get('change_reason', '定期调整'),
                    operator=session.get('staff_name', '未知')
                )
                db.session.add(discount_history)

                # 更新折扣率
                discount.discount_rate = float(data['discount_rate'])

            # 更新其他字段
            if 'effective_date' in data and data['effective_date']:
                discount.effective_date = datetime.datetime.strptime(data['effective_date'], '%Y-%m-%d').date()

            if 'expiry_date' in data:
                if data['expiry_date']:
                    discount.expiry_date = datetime.datetime.strptime(data['expiry_date'], '%Y-%m-%d').date()
                else:
                    discount.expiry_date = None

            db.session.commit()

            return jsonify({
                'success': True,
                'message': '产品折扣更新成功'
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/discounts/<int:discount_id>', methods=['DELETE'])
    @login_required
    def delete_product_discount(discount_id):
        """删除产品折扣"""
        # 验证权限
        if session.get('staff_role') != 'manager':
            return jsonify({'error': '您没有权限执行此操作'}), 403

        try:
            discount = MallProductDiscount.query.get_or_404(discount_id)

            # 记录折扣删除历史
            discount_history = MallDiscountHistory(
                mall_customer_id=discount.mall_customer_id,
                product_name=discount.product_name,
                product_type=discount.product_type,
                old_discount_rate=discount.discount_rate,
                new_discount_rate=1.0,  # 恢复为原价
                change_date=datetime.datetime.now().date(),
                change_reason='折扣删除',
                operator=session.get('staff_name', '未知')
            )
            db.session.add(discount_history)

            # 删除折扣
            db.session.delete(discount)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': '产品折扣已删除'
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/discounts/<int:discount_id>', methods=['GET'])
    @login_required
    def get_discount_details(discount_id):
        """获取单个产品折扣详情"""
        try:
            discount = MallProductDiscount.query.get_or_404(discount_id)

            result = {
                'id': discount.id,
                'mall_customer_id': discount.mall_customer_id,
                'product_name': discount.product_name,
                'product_type': discount.product_type,
                'discount_rate': discount.discount_rate,
                'effective_date': discount.effective_date.strftime('%Y-%m-%d'),
                'expiry_date': discount.expiry_date.strftime('%Y-%m-%d') if discount.expiry_date else '长期有效',
                'created_by': discount.created_by,
                'created_at': discount.created_at.strftime('%Y-%m-%d'),
                'change_reason': ''  # 这个字段不在模型中，但前端期望它存在
            }

            return jsonify(result)
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    # 月度账单管理路由
    @app.route('/api/mall_customers/<int:customer_id>/bills', methods=['GET'])
    @login_required
    def get_customer_bills(customer_id):
        """获取商场客户的月度账单列表"""
        try:
            # 验证客户存在和区域权限
            customer = MallCustomer.query.get_or_404(customer_id)
            staff_area = session.get('staff_area')
            staff_role = session.get('staff_role')
            if staff_role != 'admin' and staff_area != '总部' and staff_area and customer.area != staff_area:
                return jsonify({'error': '您没有权限查看此商场客户的账单信息'}), 403

            bills = MallMonthlyBill.query.filter_by(mall_customer_id=customer_id).order_by(MallMonthlyBill.bill_year_month.desc()).all()
            result = []

            for bill in bills:
                result.append({
                    'id': bill.id,
                    'bill_year_month': bill.bill_year_month,
                    'bill_start_date': bill.bill_start_date.strftime('%Y-%m-%d'),
                    'bill_end_date': bill.bill_end_date.strftime('%Y-%m-%d'),
                    'order_count': bill.order_count,
                    'original_amount': bill.original_amount or 0.0,  # 原始金额（折扣前）
                    'total_amount': bill.total_amount,  # 实际金额（折扣后）
                    'discount_amount': bill.discount_amount,  # 折扣金额
                    'actual_amount': bill.actual_amount,  # 应收金额
                    'payment_status': bill.payment_status,
                    'payment_date': bill.payment_date.strftime('%Y-%m-%d') if bill.payment_date else '',
                    'payment_method': bill.payment_method or '',
                    'remarks': bill.remarks or ''
                })

            return jsonify({
                'bills': result
            })
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/mall_customers/<int:customer_id>/bills', methods=['POST'])
    @login_required
    def create_monthly_bill(customer_id):
        """创建或更新商场客户月度账单"""
        try:
            mall_customer = MallCustomer.query.get(customer_id)
            if not mall_customer:
                return jsonify({'error': '商场客户不存在'}), 404

            data = request.get_json()
            if not data:
                return jsonify({'error': '请求数据为空'}), 400

            # 记录操作前状态
            previous_payment_status = None
            updated_orders_count = 0

            # 修改已存在的账单
            if 'bill_id' in data:
                bill = MallMonthlyBill.query.get(data['bill_id'])
                if not bill:
                    return jsonify({'error': '账单不存在'}), 404

                if bill.mall_customer_id != customer_id:
                    return jsonify({'error': '账单与客户不匹配'}), 400

                # 记录修改前的支付状态，用于比较变化
                previous_payment_status = bill.payment_status

                # 更新账单字段
                if 'payment_status' in data:
                    bill.payment_status = data['payment_status']

                    # 当账单状态变为"已付款"时，同步更新关联订单的支付状态
                    if data['payment_status'] == "已付款" and previous_payment_status != "已付款":
                        payment_time = datetime.datetime.now()
                        # 查找该账单周期内的所有相关订单
                        orders = Order.query.filter(
                            Order.mall_customer_id == bill.mall_customer_id,
                            Order.is_mall_order == True,
                            Order.created_at >= bill.bill_start_date,
                            Order.created_at <= bill.bill_end_date + datetime.timedelta(days=1),
                            Order.payment_status == "未付款"  # 只更新未付款的订单
                        ).all()

                        # 更新所有找到的订单状态
                        for order in orders:
                            order.payment_status = "已付款"
                            order.payment_time = payment_time
                            updated_orders_count = updated_orders_count + 1

                        print(f"已更新 {updated_orders_count} 个关联订单的支付状态为'已付款'")

                if 'payment_date' in data:
                    if data['payment_date']:
                        bill.payment_date = datetime.datetime.strptime(data['payment_date'], '%Y-%m-%d').date()
                    else:
                        bill.payment_date = None

                if 'payment_method' in data:
                    bill.payment_method = data['payment_method']

                if 'remarks' in data:
                    bill.remarks = data['remarks']

                db.session.commit()

                response_data = {
                    'success': True,
                    'message': '账单状态更新成功'
                }

            # 创建新的月度账单
            new_bill = MallMonthlyBill(
                mall_customer_id=customer_id,
                bill_year_month=data['bill_year_month'],
                bill_start_date=datetime.datetime.strptime(data['bill_start_date'], '%Y-%m-%d').date(),
                bill_end_date=datetime.datetime.strptime(data['bill_end_date'], '%Y-%m-%d').date(),
                order_count=data.get('order_count', 0),
                total_amount=data.get('total_amount', 0.0),
                discount_amount=data.get('discount_amount', 0.0),
                actual_amount=data.get('actual_amount', 0.0),
                payment_status=data.get('payment_status', '未付款'),
                payment_date=datetime.datetime.strptime(data['payment_date'], '%Y-%m-%d').date() if data.get('payment_date') else None,
                payment_method=data.get('payment_method'),
                remarks=data.get('remarks')
            )

            db.session.add(new_bill)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': '月度账单创建成功',
                'bill_id': new_bill.id
            }), 201
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/bills/<int:bill_id>', methods=['PUT'])
    @login_required
    def update_monthly_bill(bill_id):
        """更新月度账单"""
        # 验证权限
        if session.get('staff_role') != 'manager':
            return jsonify({'error': '您没有权限执行此操作'}), 403

        try:
            bill = MallMonthlyBill.query.get_or_404(bill_id)
            data = request.json

            # 记录更新前的支付状态，用于判断是否有变化
            previous_payment_status = bill.payment_status
            updated_orders_count = 0

            # 检查是否需要更新账单日期范围
            date_range_updated = False
            if 'bill_start_date' in data or 'bill_end_date' in data:
                date_range_updated = True
                if 'bill_start_date' in data and data['bill_start_date']:
                    bill.bill_start_date = datetime.datetime.strptime(data['bill_start_date'], '%Y-%m-%d').date()
                if 'bill_end_date' in data and data['bill_end_date']:
                    bill.bill_end_date = datetime.datetime.strptime(data['bill_end_date'], '%Y-%m-%d').date()

            # 如果日期范围更新了，重新计算账单统计数据
            if date_range_updated:
                # 查找新日期范围内的所有订单
                orders = Order.query.filter(
                    Order.mall_customer_id == bill.mall_customer_id,
                    Order.is_mall_order == True,
                    Order.created_at >= bill.bill_start_date,
                    Order.created_at <= bill.bill_end_date + datetime.timedelta(days=1)
                ).all()

                # 重新计算账单统计数据
                bill.order_count = len(orders)
                bill.original_amount = 0
                bill.total_amount = 0
                bill.discount_amount = 0

                for order in orders:
                    # 计算原始金额（从衣物记录中获取）
                    clothes = Clothing.query.filter_by(order_id=order.id).all()
                    order_original_amount = 0
                    for clothing in clothes:
                        original_price = clothing.original_price or clothing.price
                        quantity = clothing.quantity or 1
                        order_original_amount += original_price * quantity

                    if order_original_amount == 0:
                        order_original_amount = order.total_amount

                    bill.original_amount += order_original_amount
                    bill.total_amount += order.total_amount
                    bill.discount_amount += order.discount_amount or 0

                bill.actual_amount = bill.total_amount
                print(f"重新计算账单统计: 订单数={bill.order_count}, 原始金额={bill.original_amount}, 实际金额={bill.actual_amount}")

            # 更新账单字段
            if 'payment_status' in data:
                bill.payment_status = data['payment_status']

                # 当账单状态变为"已付款"时，同步更新关联订单的支付状态
                if data['payment_status'] == "已付款" and previous_payment_status != "已付款":
                    # 查找该账单周期内的所有相关订单
                    orders = Order.query.filter(
                        Order.mall_customer_id == bill.mall_customer_id,
                        Order.is_mall_order == True,
                        Order.created_at >= bill.bill_start_date,
                        Order.created_at <= bill.bill_end_date + datetime.timedelta(days=1),
                        Order.payment_status == "未付款"  # 只更新未付款的订单
                    ).all()

                    # 更新所有找到的订单状态
                    for order in orders:
                        order.payment_status = "已付款"
                        order.payment_time = datetime.datetime.now()
                        updated_orders_count = updated_orders_count + 1

                    print(f"已更新 {updated_orders_count} 个关联订单的支付状态为'已付款'")

            if 'payment_date' in data:
                if data['payment_date']:
                    bill.payment_date = datetime.datetime.strptime(data['payment_date'], '%Y-%m-%d').date()
                else:
                    bill.payment_date = None

            if 'payment_method' in data:
                bill.payment_method = data['payment_method']

            if 'remarks' in data:
                bill.remarks = data['remarks']

            db.session.commit()

            response_data = {
                'success': True,
                'message': '账单更新成功'
            }

            # 如果更新了订单，则在响应中包含更新信息
            if updated_orders_count > 0:
                response_data['updated_orders_count'] = updated_orders_count
                response_data['message'] = f'账单更新成功，同时更新了 {updated_orders_count} 个关联订单的支付状态'

            if date_range_updated:
                response_data['recalculated'] = True
                response_data['message'] += '，已重新计算账单统计数据'

            return jsonify(response_data)
        except Exception as e:
            db.session.rollback()
            print(f"更新月度账单出错: {str(e)}")
            return jsonify({'error': str(e)}), 500

    @app.route('/api/bills/<int:bill_id>', methods=['DELETE'])
    @login_required
    def delete_monthly_bill(bill_id):
        """删除月度账单"""
        # 验证权限
        if session.get('staff_role') != 'manager':
            return jsonify({'error': '您没有权限执行此操作'}), 403

        try:
            bill = MallMonthlyBill.query.get_or_404(bill_id)

            db.session.delete(bill)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': '月度账单已删除'
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/mall_customers/<int:customer_id>/discount_history', methods=['GET'])
    @login_required
    def get_discount_history(customer_id):
        """获取商场客户的折扣变更历史"""
        try:
            history = MallDiscountHistory.query.filter_by(mall_customer_id=customer_id).order_by(MallDiscountHistory.change_date.desc()).all()
            result = []

            for record in history:
                result.append({
                    'id': record.id,
                    'product_name': record.product_name,
                    'product_type': record.product_type,
                    'old_discount_rate': record.old_discount_rate,
                    'new_discount_rate': record.new_discount_rate,
                    'change_date': record.change_date.strftime('%Y-%m-%d'),
                    'change_reason': record.change_reason,
                    'operator': record.operator,
                    'created_at': record.created_at.strftime('%Y-%m-%d %H:%M:%S')
                })

            return jsonify({
                'history': result
            })
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    # 获取所有商品类型的API
    @app.route('/api/product_types', methods=['GET'])
    @login_required
    def get_product_types():
        """获取系统中所有衣物/产品类型"""
        try:
            # 获取所有活跃商品
            products = Product.query.filter_by(is_active=True).all()

            # 准备返回数据（不包含已废弃的基础价格）
            product_list = []
            for product in products:
                product_list.append({
                    'id': product.id,
                    'name': product.name,
                    'category': product.category,
                    'wash_price': product.wash_price,
                    'mend_price': product.mend_price,
                    'alter_price': product.alter_price,
                    'other_price': product.other_price
                })

            return jsonify({
                'success': True,
                'products': product_list
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/service_prices', methods=['GET'])
    @login_required
    def get_service_prices():
        """获取默认服务价格配置"""
        try:
            # 从数据库获取第一个商品的服务价格作为默认值
            # 如果没有商品，使用硬编码的默认值
            first_product = Product.query.filter_by(is_active=True).first()

            if first_product:
                default_prices = {
                    '洗衣': first_product.wash_price,
                    '织补': first_product.mend_price,
                    '改衣': first_product.alter_price,
                    '其他': first_product.other_price
                }
            else:
                # 如果数据库中没有商品，使用默认值
                default_prices = {
                    '洗衣': 15.0,
                    '织补': 20.0,
                    '改衣': 30.0,
                    '其他': 20.0
                }

            return jsonify({
                'success': True,
                'service_prices': default_prices
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/api/product_prices/<product_name>', methods=['GET'])
    @login_required
    def get_product_prices(product_name):
        """根据商品名称获取具体商品的服务价格"""
        try:
            # 查找指定名称的商品
            product = Product.query.filter_by(name=product_name, is_active=True).first()

            if product:
                product_prices = {
                    '洗衣': product.wash_price,
                    '织补': product.mend_price,
                    '改衣': product.alter_price,
                    '其他': product.other_price
                }
                return jsonify({
                    'success': True,
                    'product_name': product_name,
                    'service_prices': product_prices
                })
            else:
                # 如果找不到商品，返回默认价格
                default_prices = {
                    '洗衣': 15.0,
                    '织补': 20.0,
                    '改衣': 30.0,
                    '其他': 20.0
                }
                return jsonify({
                    'success': True,
                    'product_name': product_name,
                    'service_prices': default_prices,
                    'is_default': True
                })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    # 商品管理路由和API
    @app.route('/product_management')
    @login_required
    def product_management():
        """商品管理页面"""
        # 只允许管理员或经理访问
        if session.get('staff_role') not in ['admin', 'manager']:
            return redirect('/')

        return render_template('product_management.html')

    @app.route('/api/products', methods=['GET'])
    @login_required
    def get_products():
        """获取商品列表"""
        try:
            # 获取查询参数
            search = request.args.get('search', '')
            category = request.args.get('category', '')
            is_active = request.args.get('is_active', '')
            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('per_page', 10))

            # 构建查询
            query = Product.query

            # 应用搜索过滤
            if search:
                query = query.filter(Product.name.like(f'%{search}%'))

            # 应用分类过滤
            if category:
                query = query.filter(Product.category == category)

            # 应用状态过滤
            if is_active == 'true':
                query = query.filter(Product.is_active == True)
            elif is_active == 'false':
                query = query.filter(Product.is_active == False)

            # 计算总记录数
            total_count = query.count()

            # 应用分页
            products = query.order_by(Product.id).offset((page - 1) * per_page).limit(per_page).all()

            # 构建结果（不包含已废弃的基础价格）
            products_list = []
            for product in products:
                products_list.append({
                    'id': product.id,
                    'name': product.name,
                    'category': product.category,
                    'wash_price': product.wash_price,
                    'mend_price': product.mend_price,
                    'alter_price': product.alter_price,
                    'other_price': product.other_price,
                    'description': product.description,
                    'is_active': product.is_active
                })

            return jsonify({
                'products': products_list,
                'total': total_count,
                'page': page,
                'per_page': per_page,
                'total_pages': (total_count + per_page - 1) // per_page
            })
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/products/<int:product_id>', methods=['GET'])
    @login_required
    def get_product(product_id):
        """获取单个商品详情"""
        try:
            product = Product.query.get(product_id)

            if not product:
                return jsonify({'error': '商品不存在'}), 404

            # 手动构建商品数据字典（不包含已废弃的基础价格）
            product_data = {
                'id': product.id,
                'name': product.name,
                'category': product.category,
                'wash_price': product.wash_price,
                'mend_price': product.mend_price,
                'alter_price': product.alter_price,
                'other_price': product.other_price,
                'description': product.description,
                'is_active': product.is_active
            }

            return jsonify(product_data)
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/products', methods=['POST'])
    @login_required
    def add_product():
        """添加新商品"""
        # 只允许管理员或经理操作
        if session.get('staff_role') not in ['admin', 'manager']:
            return jsonify({'error': '没有权限执行此操作'}), 403

        try:
            # 获取请求数据
            data = request.json

            # 验证必填字段
            if not all(key in data for key in ['name', 'category']):
                return jsonify({'error': '缺少必填字段'}), 400

            # 检查商品名称是否已存在
            existing_product = Product.query.filter_by(name=data['name']).first()
            if existing_product:
                return jsonify({'error': '商品名称已存在'}), 400

            # 创建新商品（不包含已废弃的基础价格）
            new_product = Product(
                name=data['name'],
                category=data['category'],
                price=0.0,  # 基础价格字段设为0，保持数据库兼容性
                wash_price=float(data.get('wash_price', 15.0)),
                mend_price=float(data.get('mend_price', 20.0)),
                alter_price=float(data.get('alter_price', 30.0)),
                other_price=float(data.get('other_price', 20.0)),
                description=data.get('description', ''),
                is_active=data.get('is_active', True)
            )

            # 保存到数据库
            db.session.add(new_product)
            db.session.commit()

            # 返回新创建的商品
            product_data = {
                'id': new_product.id,
                'name': new_product.name,
                'category': new_product.category,
                'price': new_product.price,
                'wash_price': new_product.wash_price,
                'mend_price': new_product.mend_price,
                'alter_price': new_product.alter_price,
                'other_price': new_product.other_price,
                'description': new_product.description,
                'is_active': new_product.is_active
            }

            return jsonify(product_data), 201
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/products/<int:product_id>', methods=['PUT'])
    @login_required
    def update_product(product_id):
        """更新商品信息"""
        # 只允许管理员或经理操作
        if session.get('staff_role') not in ['admin', 'manager']:
            return jsonify({'error': '没有权限执行此操作'}), 403

        try:
            product = Product.query.get(product_id)

            if not product:
                return jsonify({'error': '商品不存在'}), 404

            data = request.json

            # 验证必填字段
            if not all(key in data for key in ['name', 'category']):
                return jsonify({'error': '缺少必填字段'}), 400

            # 检查商品名称是否已存在（排除当前商品）
            existing_product = Product.query.filter(
                Product.name == data['name'],
                Product.id != product_id
            ).first()

            if existing_product:
                return jsonify({'error': '商品名称已存在'}), 400

            # 更新商品信息（不包含已废弃的基础价格）
            product.name = data['name']
            product.category = data['category']
            # 基础价格字段保持不变，不再更新
            product.wash_price = float(data.get('wash_price', product.wash_price))
            product.mend_price = float(data.get('mend_price', product.mend_price))
            product.alter_price = float(data.get('alter_price', product.alter_price))
            product.other_price = float(data.get('other_price', product.other_price))
            product.description = data.get('description', product.description)
            product.is_active = data.get('is_active', product.is_active)

            db.session.commit()

            # 构建返回数据（不包含已废弃的基础价格）
            product_data = {
                'id': product.id,
                'name': product.name,
                'category': product.category,
                'wash_price': product.wash_price,
                'mend_price': product.mend_price,
                'alter_price': product.alter_price,
                'other_price': product.other_price,
                'description': product.description,
                'is_active': product.is_active
            }

            return jsonify({
                'success': True,
                'message': '商品更新成功',
                'product': product_data
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/products/<int:product_id>', methods=['DELETE'])
    @login_required
    def delete_product(product_id):
        """删除商品"""
        # 只允许管理员或经理操作
        if session.get('staff_role') not in ['admin', 'manager']:
            return jsonify({'error': '没有权限执行此操作'}), 403

        try:
            product = Product.query.get(product_id)

            if not product:
                return jsonify({'error': '商品不存在'}), 404

            # 删除商品
            db.session.delete(product)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': '商品删除成功'
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/bills/<int:bill_id>', methods=['GET'])
    @login_required
    def get_bill_details(bill_id):
        """获取单个月度账单详情"""
        try:
            bill = MallMonthlyBill.query.get_or_404(bill_id)

            result = {
                'id': bill.id,
                'mall_customer_id': bill.mall_customer_id,
                'bill_year_month': bill.bill_year_month,
                'bill_start_date': bill.bill_start_date.strftime('%Y-%m-%d'),
                'bill_end_date': bill.bill_end_date.strftime('%Y-%m-%d'),
                'order_count': bill.order_count,
                'total_amount': bill.total_amount,
                'discount_amount': bill.discount_amount,
                'actual_amount': bill.actual_amount,
                'payment_status': bill.payment_status,
                'payment_date': bill.payment_date.strftime('%Y-%m-%d') if bill.payment_date else '',
                'payment_method': bill.payment_method or '',
                'remarks': bill.remarks or ''
            }

            return jsonify(result)
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/bills/<int:bill_id>/orders', methods=['GET'])
    @login_required
    def get_bill_orders(bill_id):
        """获取账单关联的订单列表"""
        try:
            bill = MallMonthlyBill.query.get_or_404(bill_id)
            mall_customer_id = bill.mall_customer_id

            # 查询该商场客户在账单周期内的所有订单
            orders = Order.query.filter(
                Order.mall_customer_id == mall_customer_id,
                Order.is_mall_order == True,
                Order.created_at >= bill.bill_start_date,
                Order.created_at <= bill.bill_end_date + datetime.timedelta(days=1)  # 加一天确保包含结束日期当天的订单
            ).order_by(Order.created_at.desc()).all()

            result = []
            for order in orders:
                # 获取订单中的衣物信息
                clothing_items = []
                order_original_amount = 0  # 订单原始总金额
                order_actual_amount = 0    # 订单实际总金额

                for clothing in order.clothes:
                    # 正确计算原始价格：优先使用original_price，如果为空或0则使用price
                    if clothing.original_price and clothing.original_price > 0:
                        original_price = clothing.original_price
                    else:
                        # 如果没有original_price，说明这个商品没有折扣，price就是原价
                        original_price = clothing.price

                    quantity = clothing.quantity or 1
                    actual_price = clothing.price  # 实际价格（折扣后）

                    # 计算单项金额
                    item_original_amount = original_price * quantity
                    item_actual_amount = actual_price * quantity

                    # 累加到订单总金额
                    order_original_amount += item_original_amount
                    order_actual_amount += item_actual_amount

                    clothing_info = {
                        'name': clothing.name,
                        'color': clothing.color,
                        'quantity': quantity,
                        'original_price': original_price,  # 原始单价
                        'price': actual_price,             # 实际单价（折扣后）
                        'discount_rate': clothing.discount_rate or 1.0,
                        'item_original_amount': item_original_amount,  # 单项原始金额
                        'item_actual_amount': item_actual_amount       # 单项实际金额
                    }
                    clothing_items.append(clothing_info)

                # 如果没有衣物记录，使用订单的总金额
                if order_original_amount == 0:
                    order_original_amount = order.total_amount
                    order_actual_amount = order.total_amount

                # 计算订单折扣金额
                order_discount_amount = order_original_amount - order_actual_amount

                # 组装订单信息
                order_info = {
                    'id': order.id,
                    'order_number': order.order_number,
                    'created_at': order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'original_amount': order_original_amount,      # 订单原始总金额
                    'total_amount': order_actual_amount,           # 订单实际总金额（折扣后）
                    'discount_amount': order_discount_amount,      # 订单折扣金额
                    'status': order.status,
                    'payment_status': order.payment_status,
                    'operator': order.operator,
                    'clothing_count': sum(clothing.quantity or 1 for clothing in order.clothes),
                    'clothing_items': clothing_items
                }
                result.append(order_info)

            return jsonify({
                'orders': result,
                'total_count': len(result)
            })
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/bills/<int:bill_id>/export', methods=['GET'])
    @login_required
    def export_bill_orders(bill_id):
        """导出账单订单明细到Excel"""
        try:
            import openpyxl
            from openpyxl.styles import Font, Alignment, Border, Side
            from flask import make_response
            import io

            bill = MallMonthlyBill.query.get_or_404(bill_id)
            mall_customer = MallCustomer.query.get_or_404(bill.mall_customer_id)

            # 查找该账单周期内的所有相关订单
            orders = Order.query.filter(
                Order.mall_customer_id == bill.mall_customer_id,
                Order.is_mall_order == True,
                Order.created_at >= bill.bill_start_date,
                Order.created_at <= bill.bill_end_date + datetime.timedelta(days=1)
            ).order_by(Order.created_at.desc()).all()

            # 创建工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "订单明细"

            # 设置样式
            header_font = Font(bold=True, size=12)
            header_alignment = Alignment(horizontal='center', vertical='center')
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # 写入标题信息
            ws['A1'] = f"商场客户：{mall_customer.mall_name}"
            ws['A2'] = f"账单期间：{bill.bill_start_date} 至 {bill.bill_end_date}"
            ws['A3'] = f"导出时间：{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            ws['A4'] = ""  # 空行

            # 写入表头
            headers = [
                '订单号', '创建时间', '客户姓名', '客户电话', '衣物名称', '颜色',
                '数量', '原始单价', '折扣后单价', '折扣率', '小计原始金额',
                '小计实际金额', '订单原始总额', '订单实际总额', '订单折扣金额',
                '支付状态', '支付方式', '备注'
            ]

            row = 5
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=row, column=col, value=header)
                cell.font = header_font
                cell.alignment = header_alignment
                cell.border = border

            # 写入数据
            row = 6
            for order in orders:
                customer = Customer.query.get(order.customer_id)
                clothes = Clothing.query.filter_by(order_id=order.id).all()

                # 正确计算订单原始总额和实际总额
                order_original_amount = 0
                order_actual_amount = 0
                for item in clothes:
                    # 优先使用original_price，如果为空或0则使用price
                    if item.original_price and item.original_price > 0:
                        original_price = item.original_price
                    else:
                        original_price = item.price

                    quantity = item.quantity or 1
                    actual_price = item.price

                    order_original_amount += original_price * quantity
                    order_actual_amount += actual_price * quantity

                if order_original_amount == 0:
                    order_original_amount = order.total_amount
                    order_actual_amount = order.total_amount
                else:
                    # 使用计算出的实际金额，而不是订单的total_amount
                    # 因为订单的total_amount可能包含其他费用
                    pass

                # 计算订单折扣金额
                order_discount_amount = order_original_amount - order_actual_amount

                # 如果没有衣物，至少写一行订单信息
                if not clothes:
                    data = [
                        order.order_number,
                        order.created_at.strftime('%Y-%m-%d %H:%M'),
                        customer.name if customer else '未知',
                        customer.phone if customer else '未知',
                        '无衣物记录', '', 0, 0, 0, 1.0, 0, 0,
                        order_original_amount,
                        order_actual_amount,
                        order_discount_amount,
                        order.payment_status,
                        order.payment_method or '',
                        ''
                    ]
                    for col, value in enumerate(data, 1):
                        cell = ws.cell(row=row, column=col, value=value)
                        cell.border = border
                    row += 1
                else:
                    # 为每件衣物写一行
                    for i, item in enumerate(clothes):
                        # 正确计算原始价格：优先使用original_price，如果为空或0则使用price
                        if item.original_price and item.original_price > 0:
                            original_price = item.original_price
                        else:
                            original_price = item.price
                        quantity = item.quantity or 1
                        discount_rate = item.discount_rate or 1.0
                        item_original_total = original_price * quantity
                        item_actual_total = item.price * quantity

                        data = [
                            order.order_number if i == 0 else '',  # 只在第一行显示订单号
                            order.created_at.strftime('%Y-%m-%d %H:%M') if i == 0 else '',
                            customer.name if customer and i == 0 else '',
                            customer.phone if customer and i == 0 else '',
                            item.name,
                            item.color,
                            quantity,
                            original_price,
                            item.price,
                            discount_rate,
                            item_original_total,
                            item_actual_total,
                            order_original_amount if i == 0 else '',
                            order_actual_amount if i == 0 else '',      # 使用计算出的实际金额
                            order_discount_amount if i == 0 else '',    # 使用计算出的折扣金额
                            order.payment_status if i == 0 else '',
                            order.payment_method or '' if i == 0 else '',
                            item.remarks or ''
                        ]
                        for col, value in enumerate(data, 1):
                            cell = ws.cell(row=row, column=col, value=value)
                            cell.border = border
                        row += 1

            # 调整列宽
            column_widths = [15, 20, 12, 15, 15, 10, 8, 12, 12, 10, 15, 15, 15, 15, 15, 12, 12, 20]
            for i, width in enumerate(column_widths, 1):
                ws.column_dimensions[openpyxl.utils.get_column_letter(i)].width = width

            # 保存到内存
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)

            # 生成文件名（避免中文字符问题）
            import urllib.parse
            safe_customer_name = urllib.parse.quote(mall_customer.mall_name.encode('utf-8'))
            filename = f"{safe_customer_name}_{bill.bill_start_date}至{bill.bill_end_date}_订单明细_{datetime.datetime.now().strftime('%Y%m%d')}.xlsx"

            # 创建响应
            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            response.headers['Content-Disposition'] = f'attachment; filename*=UTF-8\'\'{filename}'
            response.headers['Cache-Control'] = 'no-cache'
            response.headers['Pragma'] = 'no-cache'

            return response

        except ImportError:
            return jsonify({'error': '缺少openpyxl库，无法导出Excel文件'}), 500
        except Exception as e:
            print(f"导出账单订单出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({'error': f'导出失败: {str(e)}'}), 500

    # 初始化管理员账号
    with app.app_context():
        try:
            # 检查是否有默认店员账号
            default_staff = Staff.query.filter_by(username="admin").first()

            if not default_staff:
                # 创建默认店员账号
                default_password = "admin123"
                hashed_password = generate_password_hash(default_password)
                new_staff = Staff(
                    username="admin",
                    password=hashed_password,
                    name="系统管理员",
                    role="manager",
                    area="总部",
                    is_active=True
                )
                db.session.add(new_staff)
                db.session.commit()
                print("已创建默认管理员账号：admin / admin123")
            else:
                print(f"管理员账号已存在: {default_staff.username}")
        except Exception as e:
            db.session.rollback()
            print(f"创建默认账号失败: {str(e)}")

    @app.route('/static/js/clothing-options.js')
    def clothing_options():
        """服装选项脚本"""
        return app.send_static_file('js/clothing-options.js')

    # 会员管理API路由
    @app.route('/api/members', methods=['GET'])
    @login_required
    def get_members():
        """获取会员列表"""
        try:
            # 记录开始时间
            start_time = datetime.datetime.now()
            print(f"开始获取会员列表: {start_time}")

            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 10, type=int)

            # 搜索参数
            search = request.args.get('search', '')
            status = request.args.get('status', 'all')  # all, active, inactive

            # 构建查询
            query = Customer.query

            # 应用搜索过滤
            if search:
                query = query.filter(db.or_(
                    Customer.name.like(f'%{search}%'),
                    Customer.phone.like(f'%{search}%')
                ))

            # 计算180天前的日期，用于活跃状态筛选
            active_cutoff_date = datetime.datetime.now() - datetime.timedelta(days=180)

            # 应用状态过滤
            if status == 'active':
                query = query.filter(Customer.updated_at >= active_cutoff_date)
            elif status == 'inactive':
                query = query.filter(Customer.updated_at < active_cutoff_date)

            # 获取总数，提前检查是否有结果
            total = query.count()

            # 如果没有记录，提前返回空结果
            if total == 0:
                end_time = datetime.datetime.now()
                execution_time = (end_time - start_time).total_seconds()
                print(f"会员列表查询完成，耗时: {execution_time}秒，无匹配记录")

                return jsonify({
                    'members': [],
                    'total': 0,
                    'pages': 0,
                    'current_page': page,
                    'total_pages': 0,
                    'execution_time': execution_time
                })

            # 执行分页查询 - 优化排序，添加索引
            pagination = query.order_by(Customer.updated_at.desc()).paginate(
                page=page, per_page=per_page, error_out=False
            )

            members = pagination.items

            # 构建响应 - 使用列表推导式一次性构建结果
            member_list = [{
                'id': member.id,
                'name': member.name,
                'phone': member.phone,
                'balance': member.balance,
                'gift_balance': member.gift_balance or 0.0,
                'total_balance': member.total_balance,
                'created_at': member.created_at.isoformat(),
                'updated_at': member.updated_at.isoformat()
            } for member in members]

            # 计算执行时间
            end_time = datetime.datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            print(f"会员列表查询完成，耗时: {execution_time}秒，返回 {len(member_list)} 条记录")

            # 构建响应
            return jsonify({
                'members': member_list,
                'total': pagination.total,
                'pages': pagination.pages,
                'current_page': page,
                'total_pages': pagination.pages,
                'execution_time': execution_time  # 添加执行时间到响应中，方便前端调试
            })
        except Exception as e:
            import traceback
            print(f"获取会员列表出错: {str(e)}")
            print(traceback.format_exc())
            return jsonify({'error': str(e)}), 500

    @app.route('/api/members', methods=['POST'])
    @login_required
    def create_member():
        """创建新会员"""
        try:
            data = request.json

            # 验证必填字段
            if not data.get('name') or not data.get('phone'):
                return jsonify({'error': '姓名和电话是必填字段'}), 400

            # 检查电话号码是否已存在
            existing_member = Customer.query.filter_by(phone=data['phone']).first()
            if existing_member:
                return jsonify({'error': '该电话号码已被注册'}), 400

            # 创建新会员
            new_member = Customer(
                name=data['name'],
                phone=data['phone'],
                balance=float(data.get('balance', 0.0))
            )

            db.session.add(new_member)
            db.session.commit()

            return jsonify({
                'id': new_member.id,
                'name': new_member.name,
                'phone': new_member.phone,
                'balance': new_member.balance,
                'created_at': new_member.created_at.isoformat(),
                'updated_at': new_member.updated_at.isoformat()
            }), 201
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/members/<int:member_id>', methods=['GET'])
    @login_required
    def get_member(member_id):
        """获取单个会员详情"""
        try:
            member = Customer.query.get_or_404(member_id)

            return jsonify({
                'id': member.id,
                'name': member.name,
                'phone': member.phone,
                'balance': member.balance,
                'gift_balance': member.gift_balance or 0.0,
                'total_balance': member.total_balance,
                'discount_rate': member.discount_rate or 1.0,
                'discount_expiry': member.discount_expiry.isoformat() if member.discount_expiry else None,
                'created_at': member.created_at.isoformat(),
                'updated_at': member.updated_at.isoformat()
            })
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/members/<int:member_id>', methods=['PUT'])
    @login_required
    def update_member(member_id):
        """更新会员信息"""
        try:
            member = Customer.query.get_or_404(member_id)
            data = request.json

            # 验证必填字段
            if not data.get('name') or not data.get('phone'):
                return jsonify({'error': '姓名和电话是必填字段'}), 400

            # 如果电话号码已更改，检查新电话号码是否已被其他会员使用
            if data['phone'] != member.phone:
                existing_member = Customer.query.filter_by(phone=data['phone']).first()
                if existing_member and existing_member.id != member_id:
                    return jsonify({'error': '该电话号码已被其他会员使用'}), 400

            # 更新会员信息
            member.name = data['name']
            member.phone = data['phone']
            # 余额更新应该通过充值API进行，这里不直接修改余额

            db.session.commit()

            return jsonify({
                'id': member.id,
                'name': member.name,
                'phone': member.phone,
                'balance': member.balance,
                'created_at': member.created_at.isoformat(),
                'updated_at': member.updated_at.isoformat()
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/members/<int:member_id>', methods=['DELETE'])
    @login_required
    def delete_member(member_id):
        """删除会员"""
        try:
            member = Customer.query.get_or_404(member_id)

            # 如果会员有订单或充值记录，不允许删除
            has_orders = Order.query.filter_by(customer_id=member_id).first() is not None
            has_recharges = RechargeRecord.query.filter_by(customer_id=member_id).first() is not None

            if has_orders or has_recharges:
                return jsonify({'error': '该会员有订单或充值记录，不能删除'}), 400

            db.session.delete(member)
            db.session.commit()

            return jsonify({'message': '会员已成功删除'})
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/members/<int:member_id>/recharge', methods=['POST'])
    @login_required
    def recharge_member(member_id):
        """会员账户充值"""
        try:
            member = Customer.query.get_or_404(member_id)
            data = request.json

            # 验证充值金额
            amount = float(data.get('amount', 0))
            if amount <= 0:
                return jsonify({'error': '充值金额必须大于零'}), 400

            # 验证支付方式
            payment_method = data.get('payment_method')
            if not payment_method:
                return jsonify({'error': '请选择支付方式'}), 400

            # 计算赠送金额（支持用户选择的规则）
            selected_rule_id = data.get('selected_rule_id')
            if selected_rule_id:
                # 用户选择了特定规则
                rule = RechargeGiftRule.query.filter_by(id=selected_rule_id, is_active=True).first()
                if rule and rule.min_amount <= amount:
                    if rule.gift_type == 'percentage':
                        gift_amount = amount * (rule.gift_value / 100.0)
                    else:  # fixed
                        gift_amount = rule.gift_value
                else:
                    gift_amount = 0.0
            else:
                # 使用默认最优规则
                gift_amount = calculate_gift_amount(amount)

            # 更新会员余额（包含赠送金额）
            new_balance = update_customer_balance(member, amount, gift_amount=gift_amount)

            # 创建充值记录
            recharge = RechargeRecord(
                customer_id=member_id,
                amount=amount,
                gift_amount=gift_amount,
                payment_method=payment_method,
                operator=session.get('staff_name', '未知'),
                remarks=data.get('remarks', '')
            )

            db.session.add(recharge)
            db.session.commit()

            return jsonify({
                'member_id': member_id,
                'amount': amount,
                'gift_amount': gift_amount,
                'new_balance': new_balance,
                'recharge_id': recharge.id
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/members/<int:member_id>/orders', methods=['GET'])
    @login_required
    def get_member_orders(member_id):
        """获取会员的订单历史"""
        try:
            member = Customer.query.get_or_404(member_id)

            # 查询该会员的所有订单，按创建时间降序排序
            orders = Order.query.filter_by(customer_id=member_id).order_by(Order.created_at.desc()).all()

            return jsonify({
                'member_id': member_id,
                'orders': [{
                    'id': order.id,
                    'order_number': order.order_number,
                    'total_amount': order.total_amount,
                    'payment_method': order.payment_method,
                    'payment_status': order.payment_status,
                    'status': order.status,
                    'created_at': order.created_at.isoformat()
                } for order in orders]
            })
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/members/<int:member_id>/recharges', methods=['GET'])
    @login_required
    def get_member_recharges(member_id):
        """获取会员的充值记录"""
        try:
            member = Customer.query.get_or_404(member_id)

            # 查询该会员的所有充值记录，按创建时间降序排序
            recharges = RechargeRecord.query.filter_by(customer_id=member_id).order_by(RechargeRecord.created_at.desc()).all()

            return jsonify({
                'member_id': member_id,
                'recharges': [{
                    'id': recharge.id,
                    'amount': recharge.amount,
                    'gift_amount': recharge.gift_amount or 0.0,
                    'payment_method': recharge.payment_method,
                    'operator': recharge.operator,
                    'remarks': recharge.remarks,
                    'created_at': recharge.created_at.isoformat()
                } for recharge in recharges]
            })
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/member_management')
    @login_required
    def member_management():
        """会员管理页面"""
        staff_name = session.get('staff_name', '未登录')
        staff_role = session.get('staff_role', '')

        # 允许管理员和营业员访问该页面
        if staff_role not in ['manager', 'staff']:
            flash('您没有权限访问该页面', 'error')
            return redirect(url_for('index'))

        return render_template('member_management.html', staff_name=staff_name)

    # =====================================================================
    # 会员折扣计算函数
    # =====================================================================
    def calculate_member_discount(customer_id, clothing_items):
        """计算会员折扣

        Args:
            customer_id: 客户ID
            clothing_items: 衣物列表，每个项目包含服务类型和价格

        Returns:
            dict: 包含折扣信息的字典
        """
        try:
            customer = Customer.query.get(customer_id)
            if not customer:
                return {'total_discount': 0, 'discounted_items': []}

            today = datetime.date.today()
            total_discount = 0
            discounted_items = []

            for item in clothing_items:
                item_price = float(item.get('price', 0))
                item_services = item.get('services', [])
                item_discount = 0
                applied_discount_rate = 1.0
                discount_source = None

                # 检查是否有特定服务类型的折扣
                service_discount_applied = False
                for service in item_services:
                    service_discount = MemberServiceDiscount.query.filter(
                        MemberServiceDiscount.customer_id == customer_id,
                        MemberServiceDiscount.service_type == service,
                        MemberServiceDiscount.is_active == True,
                        MemberServiceDiscount.valid_from <= today,
                        MemberServiceDiscount.valid_to >= today
                    ).first()

                    if service_discount:
                        # 使用最低的折扣率（最大的优惠）
                        if service_discount.discount_rate < applied_discount_rate:
                            applied_discount_rate = service_discount.discount_rate
                            discount_source = f"{service}服务折扣"
                        service_discount_applied = True

                # 如果没有特定服务折扣，检查整体折扣
                if not service_discount_applied and customer.discount_rate and customer.discount_rate < 1.0:
                    # 检查整体折扣是否有效
                    if not customer.discount_expiry or customer.discount_expiry >= today:
                        applied_discount_rate = customer.discount_rate
                        discount_source = "会员整体折扣"

                # 计算折扣金额
                if applied_discount_rate < 1.0:
                    discounted_price = item_price * applied_discount_rate
                    item_discount = item_price - discounted_price
                    total_discount += item_discount

                    discounted_items.append({
                        'original_price': item_price,
                        'discounted_price': discounted_price,
                        'discount_amount': item_discount,
                        'discount_rate': applied_discount_rate,
                        'discount_source': discount_source
                    })
                else:
                    discounted_items.append({
                        'original_price': item_price,
                        'discounted_price': item_price,
                        'discount_amount': 0,
                        'discount_rate': 1.0,
                        'discount_source': None
                    })

            return {
                'total_discount': total_discount,
                'discounted_items': discounted_items,
                'customer_discount_rate': customer.discount_rate or 1.0,
                'customer_discount_expiry': customer.discount_expiry.isoformat() if customer.discount_expiry else None
            }

        except Exception as e:
            print(f"计算会员折扣出错: {str(e)}")
            return {'total_discount': 0, 'discounted_items': []}

    # =====================================================================
    # 会员折扣管理API
    # =====================================================================
    @app.route('/api/members/<int:member_id>/discounts', methods=['GET'])
    @login_required
    def get_member_discounts(member_id):
        """获取会员的服务折扣列表"""
        try:
            member = Customer.query.get_or_404(member_id)
            discounts = MemberServiceDiscount.query.filter_by(customer_id=member_id).all()

            result = []
            for discount in discounts:
                result.append({
                    'id': discount.id,
                    'service_type': discount.service_type,
                    'discount_rate': discount.discount_rate,
                    'valid_from': discount.valid_from.isoformat(),
                    'valid_to': discount.valid_to.isoformat(),
                    'is_active': discount.is_active,
                    'created_at': discount.created_at.isoformat(),
                    'updated_at': discount.updated_at.isoformat()
                })

            return jsonify({
                'discounts': result,
                'member': {
                    'id': member.id,
                    'name': member.name,
                    'phone': member.phone,
                    'discount_rate': member.discount_rate or 1.0,
                    'discount_expiry': member.discount_expiry.isoformat() if member.discount_expiry else None
                }
            })
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/members/<int:member_id>/discounts', methods=['POST'])
    @login_required
    def create_member_discount(member_id):
        """创建会员服务折扣"""
        try:
            member = Customer.query.get_or_404(member_id)
            data = request.json

            # 验证必填字段
            required_fields = ['service_type', 'discount_rate', 'valid_from', 'valid_to']
            for field in required_fields:
                if field not in data:
                    return jsonify({'error': f'缺少必填字段: {field}'}), 400

            # 验证数据
            service_type = data['service_type']
            discount_rate = float(data['discount_rate'])
            valid_from = datetime.datetime.strptime(data['valid_from'], '%Y-%m-%d').date()
            valid_to = datetime.datetime.strptime(data['valid_to'], '%Y-%m-%d').date()

            if discount_rate <= 0 or discount_rate > 1:
                return jsonify({'error': '折扣率必须在0-1之间'}), 400

            if valid_from >= valid_to:
                return jsonify({'error': '生效日期必须早于失效日期'}), 400

            if service_type not in ['洗衣', '织补', '改衣', '其他']:
                return jsonify({'error': '无效的服务类型'}), 400

            # 检查是否存在重叠的折扣
            existing_discount = MemberServiceDiscount.query.filter(
                MemberServiceDiscount.customer_id == member_id,
                MemberServiceDiscount.service_type == service_type,
                MemberServiceDiscount.is_active == True,
                db.or_(
                    db.and_(MemberServiceDiscount.valid_from <= valid_from, MemberServiceDiscount.valid_to >= valid_from),
                    db.and_(MemberServiceDiscount.valid_from <= valid_to, MemberServiceDiscount.valid_to >= valid_to),
                    db.and_(MemberServiceDiscount.valid_from >= valid_from, MemberServiceDiscount.valid_to <= valid_to)
                )
            ).first()

            if existing_discount:
                return jsonify({'error': f'该服务类型在指定时间段内已存在折扣'}), 400

            # 创建新折扣
            new_discount = MemberServiceDiscount(
                customer_id=member_id,
                service_type=service_type,
                discount_rate=discount_rate,
                valid_from=valid_from,
                valid_to=valid_to,
                is_active=data.get('is_active', True)
            )

            db.session.add(new_discount)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': '服务折扣创建成功',
                'discount': {
                    'id': new_discount.id,
                    'service_type': new_discount.service_type,
                    'discount_rate': new_discount.discount_rate,
                    'valid_from': new_discount.valid_from.isoformat(),
                    'valid_to': new_discount.valid_to.isoformat(),
                    'is_active': new_discount.is_active,
                    'created_at': new_discount.created_at.isoformat(),
                    'updated_at': new_discount.updated_at.isoformat()
                }
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/member_discounts/<int:discount_id>', methods=['PUT'])
    @login_required
    def update_member_discount(discount_id):
        """更新会员服务折扣"""
        try:
            discount = MemberServiceDiscount.query.get_or_404(discount_id)
            data = request.json

            # 验证数据
            if 'discount_rate' in data:
                discount_rate = float(data['discount_rate'])
                if discount_rate <= 0 or discount_rate > 1:
                    return jsonify({'error': '折扣率必须在0-1之间'}), 400
                discount.discount_rate = discount_rate

            if 'valid_from' in data:
                valid_from = datetime.datetime.strptime(data['valid_from'], '%Y-%m-%d').date()
                discount.valid_from = valid_from

            if 'valid_to' in data:
                valid_to = datetime.datetime.strptime(data['valid_to'], '%Y-%m-%d').date()
                discount.valid_to = valid_to

            if 'is_active' in data:
                discount.is_active = bool(data['is_active'])

            # 验证日期逻辑
            if discount.valid_from >= discount.valid_to:
                return jsonify({'error': '生效日期必须早于失效日期'}), 400

            db.session.commit()

            return jsonify({
                'success': True,
                'message': '服务折扣更新成功',
                'discount': {
                    'id': discount.id,
                    'service_type': discount.service_type,
                    'discount_rate': discount.discount_rate,
                    'valid_from': discount.valid_from.isoformat(),
                    'valid_to': discount.valid_to.isoformat(),
                    'is_active': discount.is_active,
                    'created_at': discount.created_at.isoformat(),
                    'updated_at': discount.updated_at.isoformat()
                }
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/member_discounts/<int:discount_id>', methods=['DELETE'])
    @login_required
    def delete_member_discount(discount_id):
        """删除会员服务折扣"""
        try:
            discount = MemberServiceDiscount.query.get_or_404(discount_id)
            db.session.delete(discount)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': '服务折扣删除成功'
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/members/<int:member_id>/overall_discount', methods=['PUT'])
    @login_required
    def update_member_overall_discount(member_id):
        """更新会员整体折扣率"""
        try:
            member = Customer.query.get_or_404(member_id)
            data = request.json

            # 验证数据
            if 'discount_rate' in data:
                discount_rate = float(data['discount_rate'])
                if discount_rate <= 0 or discount_rate > 1:
                    return jsonify({'error': '折扣率必须在0-1之间'}), 400
                member.discount_rate = discount_rate

            if 'discount_expiry' in data:
                if data['discount_expiry']:
                    discount_expiry = datetime.datetime.strptime(data['discount_expiry'], '%Y-%m-%d').date()
                    member.discount_expiry = discount_expiry
                else:
                    member.discount_expiry = None

            db.session.commit()

            return jsonify({
                'success': True,
                'message': '会员整体折扣更新成功',
                'member': {
                    'id': member.id,
                    'name': member.name,
                    'phone': member.phone,
                    'discount_rate': member.discount_rate or 1.0,
                    'discount_expiry': member.discount_expiry.isoformat() if member.discount_expiry else None
                }
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    # 添加订单状态处理页面路由
    @app.route('/status/to_factory')
    @login_required
    def to_factory_page():
        """送洗(送至工厂)处理页面"""
        staff_name = session.get('staff_name', '未登录')
        return render_template('status_update.html',
                              staff_name=staff_name,
                              title='送洗处理',
                              icon='fa-truck-fast',
                              target_status='送至工厂中',
                              update_endpoint='/api/update_status_by_number',
                              history_endpoint='/api/status_history/送至工厂中')

    @app.route('/status/factory_in')
    @login_required
    def factory_in_page():
        """入厂处理页面"""
        staff_name = session.get('staff_name', '未登录')
        return render_template('status_update.html',
                              staff_name=staff_name,
                              title='入厂处理',
                              icon='fa-industry',
                              target_status='入厂',
                              update_endpoint='/api/update_status_by_number',
                              history_endpoint='/api/status_history/入厂')

    @app.route('/status/factory_out')
    @login_required
    def factory_out_page():
        """出厂处理页面"""
        staff_name = session.get('staff_name', '未登录')
        return render_template('status_update.html',
                              staff_name=staff_name,
                              title='出厂处理',
                              icon='fa-truck-ramp-box',
                              target_status='出厂',
                              update_endpoint='/api/update_status_by_number',
                              history_endpoint='/api/status_history/出厂')

    @app.route('/status/on_shelf')
    @login_required
    def on_shelf_page():
        """上架处理页面"""
        staff_name = session.get('staff_name', '未登录')
        return render_template('status_update.html',
                              staff_name=staff_name,
                              title='上架处理',
                              icon='fa-box',
                              target_status='已上架',
                              update_endpoint='/api/update_status_by_number',
                              history_endpoint='/api/status_history/已上架')

    @app.route('/status/self_pickup')
    @login_required
    def self_pickup_page():
        """自取处理页面"""
        staff_name = session.get('staff_name', '未登录')
        return render_template('status_update.html',
                              staff_name=staff_name,
                              title='自取处理',
                              icon='fa-hands',
                              target_status='已自取',
                              update_endpoint='/api/update_status_by_number',
                              history_endpoint='/api/status_history/已自取')

    # 添加不干胶打印页面路由
    @app.route('/sticky_label_print')
    @login_required
    def sticky_label_print():
        """不干胶打印页面"""
        staff_name = session.get('staff_name', '未登录')
        staff_id = session.get('staff_id', '')
        staff_role = session.get('staff_role', '')
        staff_area = session.get('staff_area', '')

        return render_template('sticky_label_print.html',
                            staff_name=staff_name,
                            title='不干胶打印',
                            icon='fa-print')

    # 添加获取订单信息的API接口，用于不干胶打印
    @app.route('/api/order_label/<order_number>', methods=['GET'])
    @login_required
    def get_order_label_info(order_number):
        """获取订单的标签打印信息"""
        try:
            # 获取衣物索引参数（如果有）
            clothing_index = request.args.get('clothingIndex', type=int)

            # 查询订单
            order = Order.query.filter_by(order_number=order_number).first()
            if not order:
                return jsonify({'success': False, 'error': f'订单 {order_number} 不存在'}), 404

            # 获取客户信息
            customer = Customer.query.get(order.customer_id)
            if not customer:
                return jsonify({'success': False, 'error': '客户信息不存在'}), 404

            # 获取衣物信息
            clothes = Clothing.query.filter_by(order_id=order.id).all()
            if not clothes:
                return jsonify({'success': False, 'error': '未找到衣物信息'}), 404

            # 构建标签数据
            clothes_list = []
            for i, item in enumerate(clothes, 1):
                # 解析服务和特殊需求
                services = []
                if item.services:
                    try:
                        services = json.loads(item.services)
                    except:
                        services = []

                special_requirements = {}
                if item.special_requirements:
                    try:
                        special_requirements = json.loads(item.special_requirements)
                    except:
                        special_requirements = {}

                clothes_list.append({
                    'id': item.id,
                    'name': item.name,
                    'color': item.color,
                    'price': item.price,
                    'services': services,
                    'has_accessory': 'true' if special_requirements.get('hasAccessory') else 'false',
                    'is_urgent': 'true' if special_requirements.get('isUrgent') else 'false',
                    'index': i  # 添加索引号，从1开始
                })

            # 获取当前营业员的信息
            staff_name = session.get('staff_name', '未知营业员')

            label_data = {
                'store_name': staff_name,  # 使用当前营业员名称作为门店名称
                'order_number': order.order_number,
                'customer_name': customer.name,
                'customer_phone': customer.phone,
                'address': order.address or '',
                'clothes': clothes_list,
                'total_count': len(clothes_list),
                'barcode': order.order_number,
                'selected_clothing_index': clothing_index  # 添加选中的衣物索引
            }

            return jsonify({
                'success': True,
                'label_data': label_data
            })

        except Exception as e:
            print(f"获取订单标签信息出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({'success': False, 'error': str(e)}), 500

    # 添加API接口处理订单状态更新
    @app.route('/api/update_status_by_number', methods=['POST'])
    @login_required
    def update_status_by_number():
        """根据订单号更新订单状态"""
        try:
            data = request.json
            order_number = data.get('order_number')
            target_status = data.get('target_status')

            if not order_number or not target_status:
                return jsonify({'success': False, 'error': '参数不完整'}), 400

            # 查找订单
            order = Order.query.filter_by(order_number=order_number).first()
            if not order:
                return jsonify({'success': False, 'error': f'订单 {order_number} 不存在'}), 404

            # 验证状态变更是否合法
            if not validate_status_change(order.status, target_status):
                return jsonify({
                    'success': False,
                    'error': f'非法的状态变更: 从"{order.status}"到"{target_status}"不符合订单流程'
                }), 400

            # 记录旧状态
            old_status = order.status

            # 更新订单状态
            order.status = target_status
            order.updated_at = datetime.datetime.now()

            # 记录状态变更日志
            status_log = OrderStatusLog(
                order_id=order.id,
                old_status=old_status,
                new_status=target_status,
                changed_by=session.get('staff_name', '未知'),
                remarks=f'通过{request.path}接口更新'
            )
            db.session.add(status_log)
            db.session.commit()

            # 获取客户信息
            customer = Customer.query.get(order.customer_id)
            customer_name = customer.name if customer else '未知客户'

            return jsonify({
                'success': True,
                'message': f'订单 {order_number} 状态已更新为 {target_status}',
                'order_number': order.order_number,
                'customer_name': customer_name,
                'old_status': old_status,
                'new_status': target_status
            })

        except Exception as e:
            db.session.rollback()
            print(f"更新订单状态出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({'success': False, 'error': str(e)}), 500

    # 添加API接口处理单件衣物的状态更新
    @app.route('/api/update_clothing_status', methods=['POST'])
    @login_required
    def update_clothing_status():
        """根据订单号和衣物索引更新单件衣物状态"""
        try:
            data = request.json
            order_number = data.get('order_number')
            clothing_index = data.get('clothing_index')  # 衣物索引，从1开始
            target_status = data.get('target_status')

            if not order_number or not clothing_index or not target_status:
                return jsonify({'success': False, 'error': '参数不完整'}), 400

            # 查找订单
            order = Order.query.filter_by(order_number=order_number).first()
            if not order:
                return jsonify({'success': False, 'error': f'订单 {order_number} 不存在'}), 404

            # 获取所有衣物
            clothing_items = Clothing.query.filter_by(order_id=order.id).all()
            if not clothing_items or clothing_index > len(clothing_items):
                return jsonify({'success': False, 'error': f'未找到对应的衣物 (索引: {clothing_index})'}), 404

            # 获取指定的衣物
            clothing = clothing_items[clothing_index-1]

            # 记录旧状态
            old_status = clothing.status

            # 更新衣物状态
            clothing.status = target_status

            # 同步更新订单状态（除非是管理员指定不更新订单状态）
            if not data.get('skip_order_update'):
                if validate_status_change(order.status, target_status):
                    order_old_status = order.status
                    order.status = target_status
                    order.updated_at = datetime.datetime.now()

                    # 记录订单状态变更日志
                    status_log = OrderStatusLog(
                        order_id=order.id,
                        old_status=order_old_status,
                        new_status=target_status,
                        changed_by=session.get('staff_name', '未知'),
                        remarks=f'通过衣物 {clothing.name} 水洗唛条码更新'
                    )
                    db.session.add(status_log)

            db.session.commit()

            # 获取客户信息
            customer = Customer.query.get(order.customer_id)
            customer_name = customer.name if customer else '未知客户'

            return jsonify({
                'success': True,
                'message': f'衣物 {clothing.name} 状态已更新为 {target_status}',
                'order_number': order.order_number,
                'customer_name': customer_name,
                'clothing_name': clothing.name,  # 添加衣物名称
                'old_status': old_status,
                'new_status': target_status
            })

        except Exception as e:
            db.session.rollback()
            print(f"更新衣物状态出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/status_history/<status>', methods=['GET'])
    @login_required
    def get_status_history(status):
        """获取特定状态的处理历史"""
        try:
            # 限制返回最近的10条记录
            logs = OrderStatusLog.query.filter_by(new_status=status).order_by(
                OrderStatusLog.created_at.desc()
            ).limit(10).all()

            result = []
            for log in logs:
                # 获取订单信息
                order = Order.query.get(log.order_id)
                if not order:
                    continue

                # 获取客户信息
                customer = Customer.query.get(order.customer_id)
                customer_name = customer.name if customer else '未知客户'

                # 检查备注中是否包含衣物信息
                clothing_name = None
                if log.remarks and '通过衣物' in log.remarks:
                    # 尝试从备注中提取衣物名称
                    match = re.search(r'通过衣物\s+(.+?)\s+水洗唛条码更新', log.remarks)
                    if match:
                        clothing_name = match.group(1)

                result.append({
                    'order_id': log.order_id,
                    'order_number': order.order_number,
                    'customer_name': customer_name,
                    'clothing_name': clothing_name,  # 添加衣物名称字段
                    'old_status': log.old_status,
                    'new_status': log.new_status,
                    'changed_by': log.changed_by,
                    'created_at': log.created_at.strftime('%Y-%m-%d %H:%M:%S')
                })

            return jsonify({
                'success': True,
                'history': result
            })

        except Exception as e:
            print(f"获取状态历史出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({'success': False, 'error': str(e)}), 500

    # 添加区域API接口
    @app.route('/api/areas', methods=['GET'])
    @login_required
    def get_areas():
        """获取所有区域信息"""
        try:
            # 只有管理员可以查看区域信息
            if session.get('staff_role') != 'manager':
                return jsonify({'error': '权限不足'}), 403

            # 从Staff表中获取所有唯一的区域
            areas = db.session.query(Staff.area).distinct().all()

            # 过滤掉None值
            area_list = [area[0] for area in areas if area[0]]

            # 确保总部区域在列表中
            if '总部' not in area_list:
                area_list.append('总部')

            # 排序区域列表
            area_list.sort()

            return jsonify({
                'success': True,
                'areas': area_list
            })
        except Exception as e:
            print(f"获取区域信息出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({'error': str(e)}), 500

    # 充值赠送规则管理API
    @app.route('/api/recharge_gift_rules', methods=['GET'])
    @login_required
    def get_recharge_gift_rules():
        """获取充值赠送规则列表"""
        try:
            rules = RechargeGiftRule.query.order_by(RechargeGiftRule.min_amount.asc()).all()
            return jsonify({
                'rules': [{
                    'id': rule.id,
                    'min_amount': rule.min_amount,
                    'gift_type': rule.gift_type,
                    'gift_value': rule.gift_value,
                    'is_active': rule.is_active,
                    'created_at': rule.created_at.isoformat(),
                    'updated_at': rule.updated_at.isoformat()
                } for rule in rules]
            })
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/recharge_gift_rules', methods=['POST'])
    @login_required
    def create_recharge_gift_rule():
        """创建新的充值赠送规则"""
        try:
            data = request.json

            # 验证必填字段
            required_fields = ['min_amount', 'gift_type', 'gift_value']
            for field in required_fields:
                if field not in data:
                    return jsonify({'error': f'缺少必填字段: {field}'}), 400

            # 验证数据类型和范围
            min_amount = float(data['min_amount'])
            gift_value = float(data['gift_value'])
            gift_type = data['gift_type']

            if min_amount <= 0:
                return jsonify({'error': '最小充值金额必须大于0'}), 400

            if gift_value <= 0:
                return jsonify({'error': '赠送值必须大于0'}), 400

            if gift_type not in ['percentage', 'fixed']:
                return jsonify({'error': '赠送类型必须是percentage或fixed'}), 400

            if gift_type == 'percentage' and gift_value > 100:
                return jsonify({'error': '百分比赠送不能超过100%'}), 400

            # 创建新规则
            new_rule = RechargeGiftRule(
                min_amount=min_amount,
                gift_type=gift_type,
                gift_value=gift_value,
                is_active=data.get('is_active', True)
            )

            db.session.add(new_rule)
            db.session.commit()

            return jsonify({
                'success': True,
                'rule': {
                    'id': new_rule.id,
                    'min_amount': new_rule.min_amount,
                    'gift_type': new_rule.gift_type,
                    'gift_value': new_rule.gift_value,
                    'is_active': new_rule.is_active,
                    'created_at': new_rule.created_at.isoformat(),
                    'updated_at': new_rule.updated_at.isoformat()
                }
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/recharge_gift_rules/<int:rule_id>', methods=['PUT'])
    @login_required
    def update_recharge_gift_rule(rule_id):
        """更新充值赠送规则"""
        try:
            rule = RechargeGiftRule.query.get_or_404(rule_id)
            data = request.json

            # 验证数据
            if 'min_amount' in data:
                min_amount = float(data['min_amount'])
                if min_amount <= 0:
                    return jsonify({'error': '最小充值金额必须大于0'}), 400
                rule.min_amount = min_amount

            if 'gift_value' in data:
                gift_value = float(data['gift_value'])
                if gift_value <= 0:
                    return jsonify({'error': '赠送值必须大于0'}), 400
                rule.gift_value = gift_value

            if 'gift_type' in data:
                gift_type = data['gift_type']
                if gift_type not in ['percentage', 'fixed']:
                    return jsonify({'error': '赠送类型必须是percentage或fixed'}), 400
                rule.gift_type = gift_type

            if 'is_active' in data:
                rule.is_active = bool(data['is_active'])

            # 验证百分比限制
            if rule.gift_type == 'percentage' and rule.gift_value > 100:
                return jsonify({'error': '百分比赠送不能超过100%'}), 400

            db.session.commit()

            return jsonify({
                'success': True,
                'rule': {
                    'id': rule.id,
                    'min_amount': rule.min_amount,
                    'gift_type': rule.gift_type,
                    'gift_value': rule.gift_value,
                    'is_active': rule.is_active,
                    'created_at': rule.created_at.isoformat(),
                    'updated_at': rule.updated_at.isoformat()
                }
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/recharge_gift_rules/<int:rule_id>', methods=['DELETE'])
    @login_required
    def delete_recharge_gift_rule(rule_id):
        """删除充值赠送规则"""
        try:
            rule = RechargeGiftRule.query.get_or_404(rule_id)
            db.session.delete(rule)
            db.session.commit()

            return jsonify({'success': True, 'message': '规则删除成功'})
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/calculate_gift_options', methods=['POST'])
    @login_required
    def calculate_gift_options():
        """计算所有可用的充值赠送选项"""
        try:
            data = request.json
            amount = float(data.get('amount', 0))

            if amount <= 0:
                return jsonify({'options': []})

            # 查询所有启用的赠送规则
            rules = RechargeGiftRule.query.filter(
                RechargeGiftRule.is_active == True,
                RechargeGiftRule.min_amount <= amount
            ).order_by(RechargeGiftRule.min_amount.desc()).all()

            options = []
            for rule in rules:
                if rule.gift_type == 'percentage':
                    gift_amount = amount * (rule.gift_value / 100.0)
                    description = f"充值满{rule.min_amount}元赠送{rule.gift_value}%"
                else:  # fixed
                    gift_amount = rule.gift_value
                    description = f"充值满{rule.min_amount}元赠送{rule.gift_value}元"

                options.append({
                    'rule_id': rule.id,
                    'description': description,
                    'gift_amount': gift_amount,
                    'min_amount': rule.min_amount,
                    'gift_type': rule.gift_type,
                    'gift_value': rule.gift_value
                })

            # 按赠送金额降序排序，推荐最优选项
            options.sort(key=lambda x: x['gift_amount'], reverse=True)

            return jsonify({
                'options': options,
                'recommended': options[0] if options else None
            })
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/calculate_gift', methods=['POST'])
    @login_required
    def calculate_gift():
        """计算充值赠送金额（保持向后兼容）"""
        try:
            data = request.json
            amount = float(data.get('amount', 0))

            if amount <= 0:
                return jsonify({'gift_amount': 0.0})

            gift_amount = calculate_gift_amount(amount)
            return jsonify({'gift_amount': gift_amount})
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    return app

if __name__ == '__main__':
    app = create_app('development')
    # 设置环境变量
    app.config['ENV'] = 'development'
    app.run(host='0.0.0.0', port=5000, debug=True)