/**
 * 打印样式文件
 * 包含小票和水洗唛标签的所有打印相关样式
 */

/* 水洗唛标签样式 */
.label-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.wash-label {
    width: 101mm;  /* 修改为101mm宽度 */
    height: 20mm;  /* 完全符合打印纸尺寸 */
    padding: 2px 5px; /* 减小内边距以提供更多内容空间 */
    font-size: 11px; /* 减小字体大小 */
    margin-bottom: 5px;
    position: relative;
    page-break-inside: avoid;
    background-color: white;
    display: flex; /* 使用flex布局 */
    flex-direction: row;
    border: 1px dashed #ccc; /* 添加虚线边框辅助打印定位 */
    box-sizing: border-box; /* 确保边框计入总宽度 */
    overflow: hidden; /* 防止内容溢出 */
}

.label-qr {
    text-align: center;
    margin-right: -4mm; /* 负边距调整为-4mm */
    margin-left: -8mm; /* 负边距调整为-8mm */
    display: flex;
    align-items: center;
    justify-content: center;
    width: 63mm; /* 条码区域宽度调整为63mm */
    padding: 0;
    overflow: visible; /* 允许内容溢出到相邻区域 */
    position: relative; /* 创建新的定位上下文 */
    z-index: 1; /* 确保条码区域在上层 */
    left: -4mm; /* 左偏移调整为-4mm */
}

.label-barcode {
    height: 15mm; /* 条码高度 */
    width: 63mm; /* 条码宽度调整为63mm */
    object-fit: contain; /* 确保完整显示 */
    max-width: 100%; /* 限制最大宽度 */
    margin-left: 0; /* 不需要负边距 */
    vertical-align: middle; /* 确保垂直居中 */
}

.label-info {
    font-size: 11px; /* 字体大小 */
    line-height: 1.0; /* 减小行高，使布局更紧凑 */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    padding-left: 0px;
    width: 46mm; /* 信息区域宽度调整为46mm */
    margin-left: -8mm; /* 负边距调整为-8mm */
    position: relative;
    z-index: 2;
    padding-right: 2mm;
    font-weight: bold; /* 字体加粗 */
    color: #000; /* 字体加黑 */
}

.label-service-badges {
    display: flex;
    gap: 5px; /* 间距 */
    margin-top: 2px; /* 上边距 */
    flex-wrap: wrap;
}

.service-badge {
    display: inline-block;
    padding: 1px 4px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 10px;
    margin-bottom: 2px;
    font-weight: bold; /* 字体加粗 */
}

.service-badge.urgent {
    background-color: #ffeeee;
    border-color: #ffcccc;
    color: #ff4444;
    font-weight: bold; /* 字体加粗 */
}

.service-badge.decoration {
    background-color: #eeeeff;
    border-color: #ccccff;
    color: #4444ff;
    font-weight: bold; /* 字体加粗 */
}

.label-first-row {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    border-bottom: 1px solid #999;
    padding-bottom: 1px; /* 下边距 */
    margin-bottom: 1px; /* 下边距 */
}

.label-name-price {
    flex: 1;
    display: flex;
    justify-content: space-between;
    overflow: hidden; /* 防止长文本溢出 */
    white-space: nowrap; /* 防止名称换行 */
}

.label-name-price span:first-child {
    max-width: 60%; /* 限制名称宽度 */
    text-overflow: ellipsis; /* 文本溢出显示省略号 */
    overflow: hidden;
    margin-right: 5px;
}

.label-remarks {
    font-size: 10px;
    color: #000;
    margin-top: 1px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    line-height: 1.0;
    font-weight: bold;
}


/* ===== 小票样式 - 统一管理 ===== */

/* 小票容器 */
.receipt {
    font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    font-weight: bold;
    width: 80mm;
    margin: 0 auto;
    padding: 10px;
    border: 1px dashed #ccc;
    background-color: white;
    box-sizing: border-box;
    font-size: 12px;
    line-height: 1.2;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    color: #000;
}

/* 小票头部 */
.receipt-header {
    text-align: center;
    margin-bottom: 8px;
}

.receipt-header h4 {
    margin: 3px 0;
    font-size: 14px;
}

.receipt-header p {
    margin: 5px 0 0;
    font-size: 12px;
}

/* 小票信息区域 */
.receipt-info {
    margin-bottom: 8px;
    font-size: 11px;
    line-height: 1.3;
}

.receipt-info p {
    margin: 2px 0;
}

/* 小票表格 */
.receipt-items {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 8px;
    font-size: 10px;
    table-layout: fixed;
}

/* 列宽定义 */
.receipt-items .col-name {
    width: 33%;
}

.receipt-items .col-quantity {
    width: 14%;
}

.receipt-items .col-service {
    width: 31%;
}

.receipt-items .col-price {
    width: 22%;
}

/* 表头样式 */
.receipt-items th {
    border-bottom: 1px solid #ddd;
    text-align: left;
    padding: 3px 2px;
    font-size: 11px;
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.receipt-items .th-quantity {
    text-align: center;
}

.receipt-items .th-price {
    text-align: right;
}

/* 表体样式 */
.receipt-items td {
    padding: 2px;
    font-size: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: top;
}

.receipt-items .td-quantity {
    text-align: center;
}

.receipt-items .td-price {
    text-align: right;
}

/* 小票总计 */
.receipt-total {
    border-top: 1px solid #ddd;
    padding-top: 3px;
    text-align: right;
    font-weight: bold;
    font-size: 11px;
    margin-bottom: 10px;
}

.receipt-total p {
    margin: 2px 0;
}

/* 小票底部 */
.receipt-footer {
    text-align: center;
    margin-top: 5px;
    font-size: 10px;
    border-top: 1px dashed #ccc;
    padding-top: 5px;
}

.receipt-footer p {
    margin: 1px 0;
}

/* 客户余额信息 */
.receipt-balance-info {
    border-top: 1px dashed #000;
    padding-top: 3px;
    margin-top: 5px;
}

.receipt-balance-info p {
    margin: 1px 0;
    font-size: 10px;
    line-height: 1.2;
}

/* ===== 打印弹窗样式 - 统一管理 ===== */

/* 打印弹窗容器 */
.print-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.print-modal-content {
    position: relative;
    background-color: #fff;
    margin: 5% auto;
    padding: 20px;
    width: 80%;
    max-width: 600px;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.print-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.print-modal-header h3 {
    margin: 0;
}

.close-print-modal {
    font-size: 24px;
    font-weight: bold;
    background: none;
    border: none;
    cursor: pointer;
}

.print-modal-body {
    max-height: 70vh;
    overflow-y: auto;
    margin-bottom: 15px;
}

.print-modal-footer {
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #ddd;
    padding-top: 15px;
}

.print-action-btn {
    padding: 8px 16px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.print-action-btn:hover {
    background-color: #0056b3;
}

/* ===== 水洗唛样式 - 统一管理 ===== */

/* 水洗唛容器 */
.label-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
}

/* 水洗唛标签 */
.wash-label {
    width: 101mm;  /* 修改为101mm宽度 */
    height: 20mm;  /* 统一为20mm高度 */
    padding: 2px 5px; /* 减小内边距以提供更多内容空间 */
    font-size: 11px; /* 减小字体大小 */
    margin-bottom: 10px;
    position: relative;
    page-break-inside: avoid;
    background-color: white;
    display: flex;
    flex-direction: row;
    border: 1px dashed #ccc; /* 添加虚线边框辅助打印定位 */
    box-sizing: border-box; /* 确保边框计入总宽度 */
    overflow: hidden; /* 防止内容溢出 */
}

/* 条码区域 */
.label-qr {
    text-align: center;
    margin-right: -4mm;
    margin-left: -8mm;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 63mm;
    padding: 0;
    overflow: visible; /* 允许内容溢出到相邻区域 */
    position: relative; /* 创建新的定位上下文 */
    z-index: 1; /* 确保条码区域在上层 */
    left: -4mm;
}

.label-barcode {
    height: 15mm; /* 调整高度为15mm */
    width: 63mm; /* 调整条码图像宽度为63mm */
    object-fit: contain; /* 改回contain确保完整显示 */
    max-width: 100%; /* 限制最大宽度 */
    margin-left: 0; /* 不需要负边距 */
    vertical-align: middle; /* 确保垂直居中 */
}

/* 信息区域 */
.label-info {
    font-size: 12px; /* 增大字体大小，从10px改为12px */
    line-height: 1.0; /* 减小行高，从1.1改为1.0，使布局更紧凑 */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    padding-left: 0px; /* 无左边距 */
    width: 46mm; /* 信息区域宽度调整为46mm */
    margin-left: -8mm; /* 负边距调整为-8mm */
    position: relative; /* 创建新的定位上下文 */
    z-index: 2; /* 确保信息区在条码区上层 */
    padding-right: 2mm; /* 减小右侧内边距 */
    font-weight: bold; /* 添加字体加粗 */
    color: #000; /* 添加字体加黑 */
}

.label-order-number {
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 服务标签 */
.label-service-badges {
    display: flex;
    gap: 5px; /* 减少间距，从6px改为5px，使布局更紧凑 */
    margin-top: 2px; /* 减少上边距，从3px改为2px，使布局更紧凑 */
    flex-wrap: wrap;
}

.service-badge {
    display: inline-block;
    padding: 1px 4px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 10px; /* 增大字体大小，从9px改为10px */
    margin-bottom: 2px;
    font-weight: bold; /* 添加字体加粗 */
}

.service-badge.urgent {
    background-color: #ffeeee;
    border-color: #ffcccc;
    color: #ff4444;
    font-weight: bold; /* 添加字体加粗 */
}

.service-badge.decoration {
    background-color: #eeeeff;
    border-color: #ccccff;
    color: #4444ff;
    font-weight: bold; /* 添加字体加粗 */
}

/* 标签行 */
.label-first-row {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    border-bottom: 1px solid #999;
    padding-bottom: 1px; /* 减少下边距，从2px改为1px，使布局更紧凑 */
    margin-bottom: 1px; /* 减少下边距，从2px改为1px，使布局更紧凑 */
}

.label-name-price {
    flex: 1;
    display: flex;
    justify-content: space-between;
    overflow: hidden; /* 防止长文本溢出 */
    white-space: nowrap; /* 防止名称换行 */
}

.label-name-price span:first-child {
    max-width: 60%; /* 限制名称宽度 */
    text-overflow: ellipsis; /* 文本溢出显示省略号 */
    overflow: hidden;
    margin-right: 5px;
}

.label-remarks {
    font-size: 10px; /* 增大字体大小 */
    color: #000; /* 修改颜色，使文字加黑 */
    margin-top: 1px; /* 减少上边距，使布局更紧凑 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    line-height: 1.0; /* 减小行高，使布局更紧凑 */
    font-weight: bold; /* 添加字体加粗 */
}

/* 水洗唛选择器 */
.label-dropdown-container {
    margin: 15px 0;
}

.label-select-container {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
}

.label-select-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.label-select {
    width: 100%;
    padding: 8px;
    margin-bottom: 15px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.label-selection-area {
    margin: 10px 0 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
}

#labelSelect {
    padding: 6px;
    border-radius: 4px;
    border: 1px solid #ddd;
    flex-grow: 1;
    margin: 0 10px;
}

.print-all-labels {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
}

.print-selected-label {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}


/* 打印样式 */
@media print {
    /* 隐藏所有内容 */
    body * {
        visibility: hidden !important;
        display: none !important;
    }

    /* 只显示当前打印内容 */
    #print-container, #print-container * {
        visibility: visible !important;
        display: block !important;
    }

    /* 定位打印内容 */
    #print-container {
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
        width: 100% !important;
        height: auto !important;
        background-color: white !important;
        z-index: 9999 !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    /* 确保每个标签或小票后分页 */
    .receipt, .wash-label {
        page-break-after: always !important;
        margin: 0 auto !important;
        padding: 10px !important;
        box-sizing: border-box !important;
        width: auto !important;
    }

    /* 小票打印优化 */
    .receipt {
        max-width: 80mm !important;
        width: 80mm !important;
        font-size: 11px !important;
        border: 1px dashed #000 !important;
        box-shadow: none !important;
        page-break-after: always !important;
    }

    /* 小票表格打印优化 */
    .receipt-items th {
        border-bottom: 1px solid #000 !important;
        font-weight: bold !important;
    }

    /* 打印时显示控制 */
    #print-container, #print-container * {
        visibility: visible !important;
        display: block !important;
        position: initial !important;
    }

    #print-container {
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
        width: 100% !important;
        height: auto !important;
        padding: 0 !important;
        margin: 0 !important;
        background-color: white !important;
    }

    /* 确保表格内容正确显示 */
    .receipt-items {
        display: table !important;
        visibility: visible !important;
        width: 100% !important;
        border-collapse: collapse !important;
        table-layout: fixed !important;
    }

    /* 打印时的列宽定义 - 关键修复 */
    .receipt-items .col-name {
        width: 33% !important;
    }

    .receipt-items .col-quantity {
        width: 14% !important;
    }

    .receipt-items .col-service {
        width: 31% !important;
    }

    .receipt-items .col-price {
        width: 22% !important;
    }

    /* 确保colgroup在打印时生效 */
    .receipt-items colgroup {
        display: table-column-group !important;
        visibility: visible !important;
    }

    .receipt-items col {
        display: table-column !important;
        visibility: visible !important;
    }

    .receipt-items thead {
        display: table-header-group !important;
        visibility: visible !important;
    }

    .receipt-items tbody {
        display: table-row-group !important;
        visibility: visible !important;
    }

    .receipt-items tr {
        display: table-row !important;
        visibility: visible !important;
    }

    .receipt-items th,
    .receipt-items td {
        display: table-cell !important;
        visibility: visible !important;
        padding: 2px 1px !important;
        border: none !important;
        vertical-align: top !important;
    }

    /* 确保表头在一行显示 */
    .receipt-items thead tr {
        page-break-inside: avoid !important;
        page-break-after: avoid !important;
    }

    .receipt-items th {
        font-weight: bold !important;
        border-bottom: 1px solid #000 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        text-align: left !important;
        font-size: 10px !important;
        padding: 2px 1px !important;
    }

    /* 表头对齐方式 */
    .receipt-items .th-quantity {
        text-align: center !important;
    }

    .receipt-items .th-price {
        text-align: right !important;
    }

    /* 表体对齐方式 */
    .receipt-items .td-quantity {
        text-align: center !important;
    }

    .receipt-items .td-price {
        text-align: right !important;
    }

    /* 隐藏打印时不需要的元素 */
    .close-print-modal, .print-action-btn, .print-modal-header,
    .print-modal-footer, .label-selection-area, .print-modal, .modal {
        display: none !important;
    }

    /* 水洗唛标签样式 */
    .wash-label {
        width: 101mm !important; /* 水洗唛宽度修改为101mm */
        height: 20mm !important; /* 水洗唛高度 */
        max-width: 101mm !important;
        display: flex !important; /* 恢复flex布局 */
        flex-direction: row !important;
        padding: 2px 5px !important;
        margin: 0 auto 10mm auto !important;
        page-break-after: always !important;
        page-break-inside: avoid !important;
        border: 1px dashed #ccc !important;
        box-sizing: border-box !important;
        overflow: hidden !important;
        background-color: white !important;
        font-size: 11px !important;
    }

    /* 确保条码区域正确显示 */
    .label-qr {
        text-align: center !important;
        margin-right: -4mm !important;
        margin-left: -8mm !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 63mm !important;
        padding: 0 !important;
        overflow: visible !important; /* 允许内容溢出到相邻区域 */
        position: relative !important;
        z-index: 1 !important;
        left: -4mm !important;
    }

    /* 确保条码图像正确显示 */
    .label-barcode {
        height: 15mm !important;
        width: 63mm !important;
        object-fit: contain !important;
        margin-left: 0 !important;
        vertical-align: middle !important;
        max-width: 100% !important;
    }

    /* 确保信息区域正确显示 */
    .label-info {
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: space-between !important;
        padding-left: 0px !important;
        width: 46mm !important;
        margin-left: -8mm !important;
        font-size: 11px !important;
        line-height: 1.0 !important;
        font-weight: bold !important;
        color: #000 !important;
        position: relative !important;
        z-index: 2 !important;
        padding-right: 2mm !important;
    }

    /* 确保服务标签正确显示 */
    .label-service-badges {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 5px !important;
        margin-top: 2px !important;
    }

    .service-badge {
        display: inline-block !important;
        padding: 1px 4px !important;
        background-color: #f5f5f5 !important;
        border: 1px solid #ddd !important;
        border-radius: 3px !important;
        font-size: 10px !important;
        margin-bottom: 2px !important;
        font-weight: bold !important;
    }

    .label-first-row {
        display: flex !important;
        justify-content: space-between !important;
        font-weight: bold !important;
        border-bottom: 1px solid #999 !important;
        padding-bottom: 1px !important;
        margin-bottom: 1px !important;
    }

    .label-name-price {
        flex: 1 !important;
        display: flex !important;
        justify-content: space-between !important;
        overflow: hidden !important;
        white-space: nowrap !important;
    }

    .label-remarks {
        font-size: 10px !important;
        color: #000 !important;
        margin-top: 1px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        max-width: 100% !important;
        line-height: 1.0 !important;
        font-weight: bold !important;
    }

    /* 隐藏打印时不需要的元素 */
    .print-modal, .modal, .close-print-modal, .print-action-btn,
    .print-modal-header, .print-modal-footer, .label-selection-area,
    .edit-item-btn {
        display: none !important;
        visibility: hidden !important;
    }



    /* 设置页面边距 - 针对80mm热敏打印纸优化 */
    @page {
        margin: 2mm;
        size: 80mm auto;
    }



    /* 额外的打印优化 */
    body {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* 小票表格打印优化 */
    .receipt-items tr {
        page-break-inside: avoid !important;
        page-break-after: auto !important;
    }
}
