# 水洗唛和小票打印功能维护指南

本文档详细说明了系统中水洗唛和小票打印功能的维护方法，包括如何调整尺寸、布局以及内容。

## 目录

- [文件结构](#文件结构)
- [水洗唛打印](#水洗唛打印)
  - [尺寸调整](#水洗唛尺寸调整)
  - [布局调整](#水洗唛布局调整)
  - [内容调整](#水洗唛内容调整)
  - [样式调整](#水洗唛样式调整)
- [小票打印](#小票打印)
  - [尺寸调整](#小票尺寸调整)
  - [布局调整](#小票布局调整)
  - [内容调整](#小票内容调整)
  - [样式调整](#小票样式调整)
- [常见问题](#常见问题)
- [打印调试技巧](#打印调试技巧)

## 文件结构

打印功能相关的文件主要包括：

1. **CSS 样式文件**：`static/css/print-styles.css`
   - 包含所有打印相关的样式定义

2. **JavaScript 函数文件**：`static/js/print-functions.js`
   - 包含所有打印相关的函数实现

3. **HTML 模板文件**：
   - `templates/index.html`：首页，包含打印水洗唛和小票的功能
   - `templates/history.html`：历史订单页，包含打印水洗唛和小票的功能

## 水洗唛打印

### 水洗唛尺寸调整

水洗唛的尺寸在以下位置定义：

1. **CSS 文件中**：
   ```css
   /* static/css/print-styles.css */
   .wash-label {
       width: 101mm;  /* 水洗唛宽度 */
       height: 16mm;  /* 水洗唛高度 */
       /* 其他样式... */
   }

   @media print {
       .wash-label {
           width: 101mm !important; /* 打印时的水洗唛宽度 */
           height: 16mm !important; /* 打印时的水洗唛高度 */
           /* 其他样式... */
       }
   }
   ```

2. **JavaScript 文件中**：
   ```javascript
   /* static/js/print-functions.js 中的 executePrint 函数 */
   // 设置基本可见性
   labelElements.forEach(label => {
       label.style.width = '101mm';
       label.style.height = '16mm';
       // 其他样式...
   });
   ```

要调整水洗唛尺寸，需要修改上述三处的宽度和高度值。确保 CSS 和 JavaScript 中的值保持一致，以避免预览和打印效果不一致的问题。

### 水洗唛布局调整

水洗唛的布局主要由以下几个部分组成：

1. **条码区域**（`.label-qr`）：左侧的条形码
2. **信息区域**（`.label-info`）：右侧的文本信息
   - 第一行（`.label-first-row`）：衣物名称、价格和件数
   - 客户信息行：电话和姓名
   - 营业员信息行：营业员和下单日期
   - 服务标签区域（`.label-service-badges`）：显示服务类型
   - 备注区域（`.label-remarks`）：显示订单备注

要调整布局，可以修改以下文件：

1. **CSS 文件**：
   ```css
   /* static/css/print-styles.css */
   .label-qr {
       width: 63mm; /* 条码区域宽度 */
       margin-right: -4mm; /* 负边距调整 */
       margin-left: -8mm; /* 负边距调整 */
       left: -4mm; /* 左偏移 */
       /* 其他样式... */
   }

   .label-info {
       width: 46mm; /* 信息区域宽度 */
       margin-left: -8mm; /* 负边距调整 */
       /* 其他样式... */
   }
   ```

2. **JavaScript 文件**：
   ```javascript
   /* static/js/print-functions.js 中的 executePrint 函数 */
   // 设置条码区域样式
   const qrElement = label.querySelector('.label-qr');
   if (qrElement) {
       qrElement.style.width = '63mm';
       qrElement.style.marginRight = '-4mm';
       qrElement.style.marginLeft = '-8mm';
       qrElement.style.left = '-4mm';
       // 其他样式...
   }

   // 设置信息区域样式
   const infoElement = label.querySelector('.label-info');
   if (infoElement) {
       infoElement.style.width = '46mm';
       infoElement.style.marginLeft = '-8mm';
       // 其他样式...
   }
   ```

要调整布局，需要同时修改 CSS 和 JavaScript 中的相应值。注意负边距（`margin-left`、`margin-right`）和偏移（`left`）的使用，这些值用于微调元素位置，确保布局紧凑美观。

### 水洗唛内容调整

水洗唛的内容由 `generateLabelsHTML` 函数生成，位于 `static/js/print-functions.js` 文件中：

```javascript
function generateLabelsHTML(clothesArray, orderData) {
    // 函数实现...

    clothesArray.forEach((item, index) => {
        // 生成条形码
        const barcodeImageSrc = getBarcodeForOrder(orderData.order_number || "000000", index);

        labelHTML += `
            <div class="wash-label" data-item-index="${index}">
                <div class="label-qr">
                    <img src="${barcodeImageSrc}" class="label-barcode" alt="条码">
                </div>
                <div class="label-info">
                    ${firstRowHTML}
                    <div>${customerInfo}</div>
                    <div>${operatorInfo} 下单: ${orderData.date ? orderData.date.split(' ')[0].replace(/-/g, '').substr(2) : '未知'}</div>
                    ${serviceBadgesHTML}
                    ${orderRemarks ? `<div class="label-remarks">${orderRemarks}</div>` : ''}
                </div>
            </div>
        `;
    });

    return labelHTML;
}
```

要调整水洗唛内容，可以修改 `generateLabelsHTML` 函数中的 HTML 模板字符串。例如：

- 添加新的信息行：在 `<div class="label-info">` 内添加新的 `<div>` 元素
- 修改现有信息的格式：例如，修改日期格式、电话号码显示方式等
- 调整条形码生成：修改 `getBarcodeForOrder` 函数的参数或返回值

### 水洗唛样式调整

水洗唛的样式定义在以下位置：

1. **CSS 文件**：
   ```css
   /* static/css/print-styles.css */
   .service-badge {
       display: inline-block;
       padding: 1px 4px;
       background-color: #f5f5f5;
       border: 1px solid #ddd;
       border-radius: 3px;
       font-size: 10px;
       /* 其他样式... */
   }

   .service-badge.urgent {
       background-color: #ffeeee;
       border-color: #ffcccc;
       color: #ff4444;
       /* 其他样式... */
   }
   ```

2. **JavaScript 文件**：
   ```javascript
   /* static/js/print-functions.js 中的 executePrint 函数 */
   // 设置服务标签的样式
   const serviceBadges = serviceBadgesElement.querySelectorAll('.service-badge');
   serviceBadges.forEach(badge => {
       badge.style.backgroundColor = '#f5f5f5';
       badge.style.border = '1px solid #ddd';
       badge.style.color = '#333';

       // 特殊标签样式
       if (badge.classList.contains('urgent')) {
           badge.style.backgroundColor = '#ffeeee';
           badge.style.borderColor = '#ffcccc';
           badge.style.color = '#ff4444';
       }
       // 其他样式...
   });
   ```

要调整水洗唛样式，需要同时修改 CSS 和 JavaScript 中的相应值。确保两处的值保持一致，以避免预览和打印效果不一致的问题。

## 不干胶打印

### 不干胶标签尺寸调整

不干胶标签的尺寸在 `templates/sticky_label_print.html` 文件中定义：

1. **屏幕显示样式**：
   ```css
   .sticky-label {
       width: 72mm;
       height: 50mm;
       border: 1px solid #ccc;
       padding: 3mm;
       margin: 10px auto;
       background-color: white;
       font-size: 10px;
       box-sizing: border-box;
       overflow: hidden;
   }
   ```

2. **打印时样式**：
   ```css
   @media print {
       @page {
           size: 72mm 50mm;
           margin: 0;
       }

       .sticky-label {
           position: absolute;
           left: 0;
           top: 0;
           width: 72mm !important;
           height: 50mm !important;
           margin: 0 !important;
           padding: 3mm !important;
           border: 1px solid #000 !important;
           box-sizing: border-box !important;
           overflow: hidden !important;
           font-size: 10px !important;
       }
   }
   ```

3. **条形码设置**：
   ```javascript
   JsBarcode(`#barcode-${data.order_number}`, data.order_number, {
       format: "CODE128",
       width: 1.5,
       height: 25,
       displayValue: true,
       fontSize: 8,
       margin: 2
   });
   ```

### 不干胶内容调整

不干胶标签包含以下内容：
- **店铺名称**：Soulweave改衣坊（11px，加粗，居中）
- **订单信息**：单号、日期（YYMMDD格式，8px，加粗）
- **客户信息**：电话（脱敏显示）、姓名（8px，加粗）
- **营业员信息**：营业员姓名、总件数（8px，加粗）
- **衣物信息**：序号、名称、颜色、标记（7px，加粗）
- **条形码**：订单号条码（高度20px，字体7px）

### 不干胶数据来源

不干胶标签的数据来源于 `/api/order_label/<order_number>` 接口：

```javascript
// API返回的数据结构
{
    "success": true,
    "label_data": {
        "store_name": "Soulweave改衣坊",
        "order_number": "订单号",
        "customer_name": "客户姓名",
        "customer_phone": "客户电话",
        "operator": "营业员姓名",  // 下单的营业员
        "date": "2024-01-01",     // 下单日期
        "clothes": [...],         // 衣物列表
        "total_count": 3          // 总件数
    }
}
```

字体大小和间距都经过优化以适应72mm×50mm的标签尺寸，使用flex布局确保内容充分利用标签空间。

## 小票打印

### 小票尺寸调整

小票的尺寸在以下位置定义：

1. **CSS 文件中**：
   ```css
   /* static/css/print-styles.css */
   .receipt {
       width: 80mm;
       /* 其他样式... */
   }

   @media print {
       .receipt {
           max-width: 80mm !important;
           /* 其他样式... */
       }
   }
   ```

2. **JavaScript 文件中的 `generateReceiptHTML` 函数**：
   ```javascript
   /* static/js/print-functions.js */
   function generateReceiptHTML(orderData) {
       return `
       <div class="receipt current-print-content">
           <!-- 小票内容 -->
       </div>`;
   }
   ```

要调整小票尺寸，主要修改 CSS 文件中的宽度值。小票的高度通常是自适应的，根据内容自动调整。

### 小票布局调整

小票的布局主要由以下几个部分组成：

1. **小票头部**（`.receipt-header`）：店铺名称和标题
2. **订单信息**（`.receipt-info`）：订单号、客户信息、日期等
3. **商品列表**（`.receipt-items`）：表格形式的商品列表
4. **合计信息**（`.receipt-total`）：数量、总计、折扣等
5. **小票底部**（`.receipt-footer`）：感谢语和联系方式

要调整布局，可以修改以下文件：

1. **CSS 文件**：
   ```css
   /* static/css/print-styles.css */
   .receipt-header {
       text-align: center;
       margin-bottom: 10px;
   }

   .receipt-items {
       width: 100%;
       border-collapse: collapse;
       /* 其他样式... */
   }
   ```

2. **JavaScript 文件中的 `generateReceiptHTML` 函数**：
   ```javascript
   /* static/js/print-functions.js */
   function generateReceiptHTML(orderData) {
       return `
       <div class="receipt current-print-content">
           <div class="receipt-header">
               <h4>Soulweave改衣坊</h4>
               <p>收银小票</p>
           </div>
           <!-- 其他部分 -->
       </div>`;
   }
   ```

要调整布局，可以修改 CSS 文件中的样式，或者修改 `generateReceiptHTML` 函数中的 HTML 结构。

### 小票内容调整

小票的内容由 `generateReceiptHTML` 函数生成，位于 `static/js/print-functions.js` 文件中：

```javascript
function generateReceiptHTML(orderData) {
    return `
    <div class="receipt current-print-content">
        <div class="receipt-header">
            <h4>Soulweave改衣坊</h4>
            <p>收银小票</p>
        </div>
        <div class="receipt-info">
            <p>订单号: ${orderData.order_number}</p>
            <p>客户: ${orderData.customer_name}</p>
            <p>电话: ${orderData.customer_phone}</p>
            <p>日期: ${orderData.date}</p>
            <p>收银员: ${orderData.operator}</p>
        </div>
        <table class="receipt-items" style="table-layout: fixed; width: 100%;">
            <!-- 表头和商品列表 -->
        </table>
        <div class="receipt-total">
            <!-- 合计信息 -->
        </div>
        <div class="receipt-footer" style="margin-top: 5px;">
            <p style="margin: 1px 0; font-size: 10px;">感谢您的惠顾，欢迎再次光临！</p>
            <p style="margin: 1px 0; font-size: 10px;">电话: ************</p>
        </div>
    </div>`;
}
```

要调整小票内容，可以直接修改 `generateReceiptHTML` 函数中的 HTML 模板字符串。例如：

- 修改店铺名称和标题：更改 `.receipt-header` 中的内容
- 添加或删除订单信息：在 `.receipt-info` 中添加或删除 `<p>` 元素
- 调整商品列表格式：修改 `.receipt-items` 表格的结构和列定义
- 修改底部信息：更改 `.receipt-footer` 中的内容
- 调整客户余额信息：修改 `generateBalanceInfoHTML` 函数中的内容

#### 客户余额信息显示

小票中的客户余额信息通过 `generateBalanceInfoHTML` 函数生成，该函数会：

1. **检查余额账户**：只有当客户有余额账户时才显示余额信息
2. **显示余额变化**：
   - 订单前余额：显示本次订单前的客户余额
   - 本次消费：仅在余额支付时显示消费金额
   - 订单后余额：显示本次订单后的客户余额
3. **条件显示**：对于商场客户或没有余额记录的客户，不显示此部分

要修改余额信息的显示格式，可以编辑 `generateBalanceInfoHTML` 函数：

```javascript
function generateBalanceInfoHTML(orderData) {
    if (!orderData.customer_balance_info || !orderData.customer_balance_info.has_balance_account) {
        return '';
    }

    const balanceInfo = orderData.customer_balance_info;

    return `
        <div class="receipt-balance-info" style="margin-top: 5px; border-top: 1px dashed #000; padding-top: 3px;">
            <p style="margin: 1px 0; font-size: 11px; font-weight: bold;">客户余额信息:</p>
            <p style="margin: 1px 0; font-size: 10px;">订单前余额: ¥${balanceInfo.balance_before_order.toFixed(2)}</p>
            ${balanceInfo.is_balance_payment ? `<p style="margin: 1px 0; font-size: 10px;">本次消费: ¥${balanceInfo.balance_used.toFixed(2)}</p>` : ''}
            <p style="margin: 1px 0; font-size: 10px;">订单后余额: ¥${balanceInfo.balance_after_order.toFixed(2)}</p>
        </div>`;
}
```

### 小票样式调整

小票的样式定义在以下位置：

1. **CSS 文件**：
   ```css
   /* static/css/print-styles.css */
   .receipt {
       font-family: 'Courier New', monospace;
       font-size: 12px;
       line-height: 1.2;
       /* 其他样式... */
   }

   .receipt-header h4 {
       margin: 0;
       font-size: 16px;
   }

   /* 客户余额信息样式 */
   .receipt-balance-info {
       border-top: 1px dashed #000 !important;
       padding-top: 3px !important;
       margin-top: 5px !important;
   }

   .receipt-balance-info p {
       margin: 1px 0 !important;
       font-size: 10px !important;
       line-height: 1.2 !important;
   }

   /* 小票表格样式优化 */
   .receipt-items {
       table-layout: fixed !important;
       width: 100% !important;
       border-collapse: collapse !important;
       margin: 3px 0 !important;
   }

   .receipt-items th {
       font-size: 10px !important;
       padding: 2px 1px !important;
       font-weight: bold !important;
       line-height: 1.1 !important;
   }

   .receipt-items td {
       font-size: 9px !important;
       padding: 1px !important;
       line-height: 1.1 !important;
       vertical-align: top !important;
   }
   ```

#### 表格布局优化

小票中的商品列表表格经过优化，确保在80mm小票宽度内正确显示：

**列宽比例**：
- 品名：35%（显示商品名称和颜色）
- 数量：15%（显示数量信息）
- 服务：30%（显示服务类型）
- 单价：20%（显示价格信息）

**样式特点**：
1. **固定表格布局**：使用 `table-layout: fixed` 确保列宽严格按比例分配
2. **文本溢出处理**：长文本使用省略号显示，避免换行
3. **字体大小优化**：表头10px，内容9px，确保在小票上清晰可读
4. **边框和间距**：表头有下边框分隔，适当的内边距确保美观

**修改列宽比例**：
如需调整列宽，需要同时修改以下位置：
1. JavaScript函数中的 `colgroup` 设置
2. CSS文件中的对应样式

```javascript
// 在 generateReceiptHTML 函数中
<colgroup>
    <col style="width: 35%;">  <!-- 品名列 -->
    <col style="width: 15%;">  <!-- 数量列 -->
    <col style="width: 30%;">  <!-- 服务列 -->
    <col style="width: 20%;">  <!-- 单价列 -->
</colgroup>
   ```

2. **JavaScript 文件中的 `generateReceiptHTML` 函数**：
   ```javascript
   /* static/js/print-functions.js */
   function generateReceiptHTML(orderData) {
       return `
       <div class="receipt current-print-content">
           <!-- 内联样式 -->
           <div class="receipt-header">
               <h4>Soulweave改衣坊</h4>
               <p>收银小票</p>
           </div>
           <!-- 其他部分 -->
       </div>`;
   }
   ```

要调整小票样式，可以修改 CSS 文件中的样式，或者修改 `generateReceiptHTML` 函数中的内联样式。

## 常见问题

### 1. 预览正确但打印布局错乱

这通常是因为打印样式（`@media print`）没有正确应用。解决方法：

1. 确保 CSS 文件中的 `@media print` 样式正确定义
2. 在 JavaScript 中添加内联打印样式
3. 使用 `!important` 确保打印样式优先级高于其他样式

关键代码示例：
```javascript
// 添加内联打印样式
const styleElement = document.createElement('style');
styleElement.id = 'print-label-styles';
styleElement.textContent = `
    @media print {
        /* 打印样式定义 */
        #print-container .wash-label {
            display: flex !important;
            /* 其他样式... */
        }
    }
`;
document.head.appendChild(styleElement);
```

### 2. 条形码不显示或显示不完整

这通常是因为条形码图像的尺寸或可见性设置不正确。解决方法：

1. 确保条形码图像的 `width` 和 `height` 设置合适
2. 设置 `visibility: visible` 和适当的 `display` 值
3. 检查 `getBarcodeForOrder` 函数是否正确生成条形码 URL

关键代码示例：
```javascript
// 设置条码图像样式
const barcodeElement = label.querySelector('.label-barcode');
if (barcodeElement) {
    barcodeElement.style.visibility = 'visible';
    barcodeElement.style.height = '15mm';
    barcodeElement.style.width = '63mm';
    barcodeElement.style.objectFit = 'contain';
}
```

### 3. 打印内容被截断

这通常是因为打印区域尺寸设置不正确。解决方法：

1. 调整水洗唛或小票的宽度和高度
2. 设置适当的页边距（`@page { margin: 0.5cm; }`）
3. 使用 `page-break-after: always` 确保每个标签单独分页

关键代码示例：
```css
@media print {
    @page {
        margin: 0.5cm;
        size: auto;
    }

    .wash-label {
        page-break-after: always !important;
        page-break-inside: avoid !important;
    }
}
```

## 打印调试技巧

1. **使用浏览器打印预览**：
   - 在浏览器中使用 Ctrl+P 或 Cmd+P 打开打印预览
   - 检查预览中的布局是否符合预期

2. **使用浏览器开发者工具**：
   - 打开开发者工具（F12 或 Ctrl+Shift+I）
   - 在 Elements 面板中检查元素的样式
   - 在 Console 面板中查看日志输出

3. **模拟打印媒体**：
   - 在开发者工具中，打开 Rendering 面板
   - 在 Emulate CSS media 下拉菜单中选择 print
   - 这样可以在不实际打印的情况下查看打印样式效果

4. **添加调试日志**：
   - 在关键函数中添加 `console.log` 语句
   - 记录元素尺寸、样式和内容等信息

5. **检查打印样式**：
   - 确保 `@media print` 样式正确定义
   - 检查样式优先级，使用 `!important` 确保打印样式优先级高于其他样式

## 最新修复说明 (2025-01-28)

### 小票表格格式修复

针对小票打印时表格列错位的问题，进行了以下重要修复：

#### 1. 表格HTML结构优化

**修改文件**: `static/js/print-functions.js`

**主要改进**:
- 为表格添加了明确的类选择器，确保CSS样式精确应用
- 使用 `colgroup` 定义列宽，提供更好的表格布局控制
- 为每个表头和数据单元格添加专用的CSS类

```javascript
// 修复前的表格结构
<table class="receipt-items">
    <colgroup>
        <col style="width: 35%;">
        <col style="width: 15%;">
        <col style="width: 30%;">
        <col style="width: 20%;">
    </colgroup>
    // ...
</table>

// 修复后的表格结构
<table class="receipt-items">
    <colgroup>
        <col class="col-name">
        <col class="col-quantity">
        <col class="col-service">
        <col class="col-price">
    </colgroup>
    <thead>
        <tr>
            <th class="th-name">品名</th>
            <th class="th-quantity">数量</th>
            <th class="th-service">服务</th>
            <th class="th-price">单价</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td class="td-name">${item.name || '未知'}</td>
            <td class="td-quantity">${item.quantity || 1}</td>
            <td class="td-service">${formatReceiptServiceInfo(item)}</td>
            <td class="td-price">¥${(item.price || 0).toFixed(2)}</td>
        </tr>
    </tbody>
</table>
```

#### 2. CSS样式重构

**修改文件**: `static/css/print-styles.css`

**主要改进**:
- 使用类选择器替代nth-child选择器，提高样式应用的可靠性
- 统一预览和打印样式，确保显示效果一致
- 优化表格布局算法，防止列宽变化

```css
/* 修复前的样式 */
.receipt-items colgroup col:nth-child(1) {
    width: 35%;
}

/* 修复后的样式 */
.receipt-items .col-name {
    width: 35% !important;
}

.receipt-items .th-name {
    text-align: left !important;
}

.receipt-items .td-name {
    text-align: left !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}
```

#### 3. 打印兼容性增强

**主要改进**:
- 添加了跨浏览器兼容性设置
- 强化了表格布局稳定性
- 优化了80mm热敏打印纸的显示效果

```css
@media print {
    /* 额外的打印优化 */
    body {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* 确保表格在所有浏览器中正确显示 */
    .receipt-items,
    .receipt-items * {
        -webkit-box-sizing: border-box !important;
        -moz-box-sizing: border-box !important;
        box-sizing: border-box !important;
    }

    /* 防止表格内容被分页 */
    .receipt-items {
        page-break-inside: avoid !important;
    }

    /* 强制表格布局稳定性 */
    .receipt-items colgroup,
    .receipt-items colgroup col {
        display: table-column !important;
    }

    /* 确保小票在80mm宽度下正确显示 */
    .receipt {
        max-width: 80mm !important;
        width: 80mm !important;
        font-size: 11px !important;
    }
}
```

#### 4. 测试验证

**创建文件**: `test_receipt_print.html`

为了验证修复效果，创建了专门的测试页面：
- 生成标准格式的测试小票
- 包含不同长度的商品名称和服务类型
- 提供打印预览和实际打印测试功能

#### 5. 修复效果

经过以上修复，小票打印功能现在具备以下特点：

1. **表格对齐精确**: 表头和数据行严格按照35%、15%、30%、20%的比例对齐
2. **预览打印一致**: 打印预览和实际打印效果完全一致
3. **文本处理优化**: 长文本自动使用省略号处理，避免列宽变化
4. **跨浏览器兼容**: 在Chrome、Firefox、Edge等主流浏览器中表现一致
5. **热敏打印优化**: 专门针对80mm热敏打印纸进行了优化

#### 6. 维护建议

1. **测试流程**: 每次修改打印相关代码后，使用 `test_receipt_print.html` 进行测试
2. **样式同步**: 修改表格样式时，确保预览样式和打印样式保持一致
3. **浏览器测试**: 在不同浏览器中测试打印效果，确保兼容性
4. **实际打印验证**: 在实际的80mm热敏打印机上验证最终效果

### 客户余额显示修复 (2025-01-28)

针对小票打印中客户余额信息不显示的问题，进行了以下修复：

#### 1. 问题分析

**问题描述**: 小票打印时客户余额信息没有显示，影响了余额支付客户的账单记录。

**原因分析**:
- `generateReceiptHTML`函数中使用了简化的余额计算逻辑
- 没有正确调用专门的`generateBalanceInfoHTML`函数
- 缺少对多种余额数据格式的兼容性处理

#### 2. 修复内容

**修改文件**: `static/js/print-functions.js`

**主要改进**:

1. **统一余额信息生成**: 修改`generateReceiptHTML`函数，使用专门的`generateBalanceInfoHTML`函数处理余额信息

```javascript
// 修复前
function generateReceiptHTML(orderData) {
    // 计算客户余额信息
    let balanceInfo = '';
    if (orderData.customer_balance !== undefined && orderData.customer_balance !== null) {
        const beforeBalance = parseFloat(orderData.customer_balance) || 0;
        const actualAmount = parseFloat(orderData.actual_amount) || 0;
        const afterBalance = beforeBalance - actualAmount;

        balanceInfo = `
            <div class="receipt-balance-info">
                <p>交易前余额: ¥${beforeBalance.toFixed(2)}</p>
                <p>本次消费: ¥${actualAmount.toFixed(2)}</p>
                <p>交易后余额: ¥${afterBalance.toFixed(2)}</p>
            </div>
        `;
    }
    // ...
}

// 修复后
function generateReceiptHTML(orderData) {
    // 生成客户余额信息 - 使用专门的函数处理
    const balanceInfo = generateBalanceInfoHTML(orderData);
    // ...
}
```

2. **增强余额信息处理**: 优化`generateBalanceInfoHTML`函数，支持多种数据格式

```javascript
function generateBalanceInfoHTML(orderData) {
    // 检查多种可能的余额信息格式
    let balanceInfo = null;
    let hasBalanceAccount = false;

    // 方式1：检查 customer_balance_info 对象
    if (orderData.customer_balance_info && orderData.customer_balance_info.has_balance_account) {
        balanceInfo = orderData.customer_balance_info;
        hasBalanceAccount = true;
    }
    // 方式2：检查直接的 customer_balance 字段
    else if (orderData.customer_balance !== undefined && orderData.customer_balance !== null) {
        const beforeBalance = parseFloat(orderData.customer_balance) || 0;
        const actualAmount = parseFloat(orderData.actual_amount) || 0;
        const afterBalance = beforeBalance - actualAmount;

        // 只有当客户有余额账户时才显示余额信息
        if (beforeBalance > 0 || orderData.payment_method === '余额') {
            balanceInfo = {
                balance_before_order: beforeBalance,
                balance_after_order: afterBalance,
                balance_used: orderData.payment_method === '余额' ? actualAmount : 0,
                is_balance_payment: orderData.payment_method === '余额'
            };
            hasBalanceAccount = true;
        }
    }
    // 方式3：检查是否为余额支付
    else if (orderData.payment_method === '余额') {
        // 如果是余额支付但没有余额信息，尝试从其他字段获取
        const actualAmount = parseFloat(orderData.actual_amount) || 0;
        balanceInfo = {
            balance_before_order: actualAmount, // 假设至少有消费金额的余额
            balance_after_order: 0,
            balance_used: actualAmount,
            is_balance_payment: true
        };
        hasBalanceAccount = true;
    }

    // 如果没有余额信息，返回空字符串
    if (!hasBalanceAccount || !balanceInfo) {
        return '';
    }

    return `
        <div class="receipt-balance-info" style="margin-top: 5px; border-top: 1px dashed #000; padding-top: 3px;">
            <p style="margin: 1px 0; font-size: 11px; font-weight: bold;">客户余额信息:</p>
            <p style="margin: 1px 0; font-size: 10px;">交易前余额: ¥${balanceInfo.balance_before_order.toFixed(2)}</p>
            ${balanceInfo.is_balance_payment ? `<p style="margin: 1px 0; font-size: 10px;">本次消费: ¥${balanceInfo.balance_used.toFixed(2)}</p>` : ''}
            <p style="margin: 1px 0; font-size: 10px;">交易后余额: ¥${balanceInfo.balance_after_order.toFixed(2)}</p>
        </div>`;
}
```

3. **CSS样式完善**: 添加预览状态下的余额信息样式

```css
/* 客户余额信息样式 - 预览状态 */
.receipt-balance-info {
    border-top: 1px dashed #000;
    padding-top: 3px;
    margin-top: 5px;
}

.receipt-balance-info p {
    margin: 1px 0;
    font-size: 10px;
    line-height: 1.2;
}
```

#### 3. 测试验证

**更新测试页面**: `test_receipt_print.html`

添加了两种测试场景：
1. **有余额信息的小票**: 包含完整的客户余额信息显示
2. **无余额信息的小票**: 普通现金支付，不显示余额信息

#### 4. 修复效果

经过修复，客户余额显示功能现在具备：

1. **多格式兼容**: 支持`customer_balance_info`对象和`customer_balance`字段两种数据格式
2. **智能判断**: 根据支付方式和余额数据自动判断是否显示余额信息
3. **完整信息**: 显示交易前余额、本次消费金额、交易后余额
4. **样式统一**: 预览和打印状态下的样式完全一致
5. **条件显示**: 只有余额支付或有余额账户的客户才显示余额信息

#### 5. 使用说明

- **余额支付订单**: 自动显示完整的余额变化信息
- **现金支付订单**: 如果客户有余额账户且余额大于0，也会显示余额信息
- **商场客户**: 没有余额账户的客户不显示余额信息
- **数据兼容**: 支持后端返回的各种余额数据格式
